# Python项目加密方案总结

## 📋 方案概述

本项目实现了一套完整的Python程序加密保护方案，包含网络验证、Cython编译、反调试等多层保护机制。

## 🔧 核心组件

### 1. 验证系统
- **服务器端**: `auth_server.py` - Flask验证服务器
- **客户端**: `auth_client.py` - 验证客户端模块
- **数据库**: `license.db` - SQLite数据库存储卡密信息

### 2. 管理工具
- **图形界面**: `server_ui.py` - 服务器管理界面（推荐）
- **命令行**: `demo_server_manager.py` - 命令行管理工具

### 3. 安全模块
- **反调试**: `anti_debug.py` - 多层反调试检测
- **卡密界面**: `license_dialog.py` - 美观的卡密输入界面

### 4. 编译工具
- **Cython编译**: `cython_build.py` - 核心模块编译脚本
- **完整构建**: `complete_build.py` - 一键编译+打包脚本

### 5. 主程序
- **加密版本**: `ZhangTing-v5.1.py` - 集成验证的主程序

## 🚀 使用流程

### 开发阶段

#### 1. 启动验证服务器
```bash
python auth_server.py
```

#### 2. 管理卡密
```bash
# 图形界面管理（推荐）
python server_ui.py

# 或命令行管理
python demo_server_manager.py
```

#### 3. 测试程序
```bash
python ZhangTing-v5.1.py
```

### 发布阶段

#### 1. Cython编译（可选）
```bash
python cython_build.py
```

#### 2. 完整构建
```bash
python complete_build.py
```

## 🔐 安全特性

### 1. 网络验证
- ✅ 服务器端验证，无法离线破解
- ✅ AES-256 + HMAC双重加密通信
- ✅ 硬件ID绑定，防止多设备使用
- ✅ 项目代码隔离，不同项目独立验证

### 2. 反调试保护
- ✅ 调试器检测（多种方法）
- ✅ 虚拟机环境检测
- ✅ 进程监控检测
- ✅ 时间检测防止暂停调试

### 3. 代码保护
- ✅ Cython编译核心模块为C扩展
- ✅ 关键算法编译保护
- ✅ 导入包装器保持兼容性

### 4. 用户体验
- ✅ 自动保存卡密功能
- ✅ 美观的卡密输入界面
- ✅ 详细的错误提示信息
- ✅ 联系信息显示

## 📊 项目结构

```
项目根目录/
├── 核心验证模块/
│   ├── auth_server.py          # 验证服务器
│   ├── auth_client.py          # 验证客户端
│   ├── anti_debug.py           # 反调试模块
│   └── license_dialog.py       # 卡密输入界面
├── 管理工具/
│   ├── server_ui.py            # 图形界面管理
│   └── demo_server_manager.py  # 命令行管理
├── 编译工具/
│   ├── cython_build.py         # Cython编译脚本
│   └── complete_build.py       # 完整构建脚本
├── 主程序/
│   ├── ZhangTing-v5.1.py       # 加密版主程序
│   └── GetDayZhangTing.py      # API模块
├── 数据库/
│   └── license.db              # 卡密数据库
└── 发布目录/
    └── release/                # 生成的exe文件
```

## 🔧 配置说明

### 验证服务器配置
```python
# auth_server.py 中的配置
CONFIG = {
    'HOST': '0.0.0.0',
    'PORT': 16888,
    'SECRET_KEY': 'your-secret-key-change-this',
    'DATABASE': 'license.db'
}
```

### 客户端配置
```python
# ZhangTing-v5.1.py 中的配置
AUTH_CONFIG = {
    'SERVER_URL': 'http://**************:16888',
    'SECRET_KEY': 'your-secret-key-change-this',
    'PROJECT_CODE': 'ZHANGTING_V51',
    'PROJECT_NAME': '异动解读工具v5.1',
}
```

## 📈 破解难度评估

| 保护层级 | 技术实现 | 破解难度 | 说明 |
|---------|---------|---------|------|
| 网络验证 | 服务器端验证 | ⭐⭐⭐⭐⭐ | 需要逆向网络协议 |
| 加密通信 | AES-256+HMAC | ⭐⭐⭐⭐⭐ | 密钥破解几乎不可能 |
| Cython编译 | C扩展模块 | ⭐⭐⭐⭐⭐ | C代码反编译极困难 |
| 反调试 | 多层检测 | ⭐⭐⭐⭐ | 需要专业反调试技术 |
| 硬件绑定 | HWID验证 | ⭐⭐⭐⭐ | 防止多设备传播 |

**总体评估**: 对于一般破解者来说**几乎不可能**，对于专业破解团队需要**大量时间和资源**

## 🛠️ 维护指南

### 1. 添加新卡密
```bash
# 使用图形界面
python server_ui.py

# 或使用命令行
python demo_server_manager.py
```

### 2. 查看验证日志
- 图形界面：server_ui.py -> 验证日志选项卡
- 命令行：demo_server_manager.py -> 选项2

### 3. 统计信息
- 图形界面：server_ui.py -> 统计信息选项卡
- 数据库直接查询：SQLite工具

### 4. 更新程序
1. 修改主程序代码
2. 运行 `python cython_build.py` 编译核心模块
3. 运行 `python complete_build.py` 生成新的exe文件

## ⚠️ 注意事项

### 1. 服务器部署
- 确保服务器稳定运行
- 定期备份数据库文件
- 监控服务器日志

### 2. 密钥安全
- 定期更换SECRET_KEY
- 不要在代码中硬编码敏感信息
- 使用HTTPS部署服务器

### 3. 编译环境
- 确保安装了Cython和PyInstaller
- 编译前备份原始文件
- 测试编译后的程序功能

### 4. 用户支持
- 提供清晰的购买渠道信息
- 及时处理用户验证问题
- 保持联系方式有效

## 📞 技术支持

- **作者QQ**: 2267617536
- **项目版本**: v5.1 加密版
- **最后更新**: 2025-07-29

---

**本方案提供了企业级的Python程序保护能力，适用于需要高安全性的商业软件发布。**
