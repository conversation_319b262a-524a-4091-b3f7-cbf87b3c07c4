#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python加密验证服务器
基于Flask框架的卡密验证API服务

功能特性:
- 卡密验证与HWID绑定
- SQLite数据库存储
- AES加密通信
- 请求签名验证
- 防重放攻击
- 详细日志记录

作者: 股中掘金
版本: 1.0
"""

import os
import sqlite3
import hashlib
import hmac
import time
import json
import base64
from datetime import datetime, timedelta
from flask import Flask, request, jsonify
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

app = Flask(__name__)

# 配置
CONFIG = {
    'SECRET_KEY': 'your-secret-key-change-this',  # 请修改为随机密钥
    'DATABASE': 'license.db',
    'MAX_REQUEST_AGE': 300,  # 请求有效期(秒)
    'ENCRYPTION_KEY': None,  # 将在初始化时生成
}

class AuthServer:
    """验证服务器主类"""
    
    def __init__(self):
        self.init_database()
        self.init_encryption()
    
    def init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(CONFIG['DATABASE'])
        cursor = conn.cursor()
        
        # 创建卡密表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS license_keys (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                key_code TEXT UNIQUE NOT NULL,
                project_code TEXT NOT NULL DEFAULT 'DEFAULT',
                hwid TEXT,
                status INTEGER DEFAULT 1,
                expire_date TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                last_used TEXT,
                use_count INTEGER DEFAULT 0,
                max_uses INTEGER DEFAULT -1
            )
        ''')
        
        # 创建验证日志表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS auth_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                key_code TEXT,
                project_code TEXT,
                hwid TEXT,
                ip_address TEXT,
                result TEXT,
                error_msg TEXT,
                timestamp TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
        print("数据库初始化完成")
    
    def init_encryption(self):
        """初始化加密"""
        # 生成加密密钥
        password = CONFIG['SECRET_KEY'].encode()
        salt = b'salt_1234567890'  # 实际使用时应该随机生成
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password))
        CONFIG['ENCRYPTION_KEY'] = Fernet(key)
        print("加密模块初始化完成")
    
    def encrypt_data(self, data):
        """加密数据"""
        if isinstance(data, dict):
            data = json.dumps(data)
        return CONFIG['ENCRYPTION_KEY'].encrypt(data.encode()).decode()
    
    def decrypt_data(self, encrypted_data):
        """解密数据"""
        try:
            decrypted = CONFIG['ENCRYPTION_KEY'].decrypt(encrypted_data.encode())
            return json.loads(decrypted.decode())
        except Exception as e:
            print(f"解密失败: {e}")
            return None
    
    def verify_signature(self, data, signature, timestamp):
        """验证请求签名"""
        # 检查时间戳
        current_time = int(time.time())
        if abs(current_time - timestamp) > CONFIG['MAX_REQUEST_AGE']:
            return False, "请求已过期"
        
        # 验证签名
        message = f"{data}{timestamp}".encode()
        expected_signature = hmac.new(
            CONFIG['SECRET_KEY'].encode(),
            message,
            hashlib.sha256
        ).hexdigest()
        
        if not hmac.compare_digest(signature, expected_signature):
            return False, "签名验证失败"
        
        return True, "签名验证成功"
    
    def log_auth_attempt(self, key_code, project_code, hwid, ip_address, result, error_msg=None):
        """记录验证尝试"""
        conn = sqlite3.connect(CONFIG['DATABASE'])
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO auth_logs (key_code, project_code, hwid, ip_address, result, error_msg)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (key_code, project_code, hwid, ip_address, result, error_msg))

        conn.commit()
        conn.close()
    
    def validate_license(self, key_code, project_code, hwid):
        """验证卡密"""
        conn = sqlite3.connect(CONFIG['DATABASE'])
        cursor = conn.cursor()

        # 查询卡密信息
        cursor.execute('''
            SELECT id, hwid, status, expire_date, use_count, max_uses
            FROM license_keys WHERE key_code = ? AND project_code = ?
        ''', (key_code, project_code))
        
        result = cursor.fetchone()
        
        if not result:
            conn.close()
            return False, "卡密不存在"
        
        license_id, bound_hwid, status, expire_date, use_count, max_uses = result
        
        # 检查状态
        if status != 1:
            conn.close()
            return False, "卡密已被禁用"
        
        # 检查过期时间
        if expire_date:
            expire_dt = datetime.fromisoformat(expire_date)
            if datetime.now() > expire_dt:
                conn.close()
                return False, "卡密已过期"
        
        # 检查使用次数限制
        if max_uses > 0 and use_count >= max_uses:
            conn.close()
            return False, "卡密使用次数已达上限"
        
        # 检查HWID绑定
        if bound_hwid is None:
            # 首次使用，绑定HWID
            cursor.execute('''
                UPDATE license_keys 
                SET hwid = ?, last_used = ?, use_count = use_count + 1
                WHERE id = ?
            ''', (hwid, datetime.now().isoformat(), license_id))
        elif bound_hwid != hwid:
            conn.close()
            return False, "硬件ID不匹配"
        else:
            # 更新使用记录
            cursor.execute('''
                UPDATE license_keys 
                SET last_used = ?, use_count = use_count + 1
                WHERE id = ?
            ''', (datetime.now().isoformat(), license_id))
        
        conn.commit()
        conn.close()
        return True, "验证成功"

# 创建服务器实例
auth_server = AuthServer()

@app.route('/api/auth', methods=['POST'])
def authenticate():
    """验证API端点"""
    try:
        # 获取请求数据
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'message': '无效的请求数据'}), 400
        
        # 验证必需字段
        required_fields = ['encrypted_data', 'signature', 'timestamp']
        for field in required_fields:
            if field not in data:
                return jsonify({'success': False, 'message': f'缺少字段: {field}'}), 400
        
        # 验证签名
        is_valid, msg = auth_server.verify_signature(
            data['encrypted_data'], 
            data['signature'], 
            data['timestamp']
        )
        
        if not is_valid:
            return jsonify({'success': False, 'message': msg}), 401
        
        # 解密数据
        decrypted_data = auth_server.decrypt_data(data['encrypted_data'])
        if not decrypted_data:
            return jsonify({'success': False, 'message': '数据解密失败'}), 400
        
        # 验证卡密
        key_code = decrypted_data.get('key_code')
        project_code = decrypted_data.get('project_code', 'DEFAULT')
        hwid = decrypted_data.get('hwid')

        if not key_code or not hwid:
            return jsonify({'success': False, 'message': '缺少卡密或硬件ID'}), 400

        # 执行验证
        is_valid, message = auth_server.validate_license(key_code, project_code, hwid)

        # 记录日志
        auth_server.log_auth_attempt(
            key_code, project_code, hwid, request.remote_addr,
            'success' if is_valid else 'failed',
            None if is_valid else message
        )
        
        # 返回结果
        response_data = {
            'success': is_valid,
            'message': message,
            'timestamp': int(time.time())
        }
        
        # 加密响应
        encrypted_response = auth_server.encrypt_data(response_data)
        
        return jsonify({
            'encrypted_data': encrypted_response,
            'timestamp': int(time.time())
        })
        
    except Exception as e:
        print(f"验证API异常: {e}")
        return jsonify({'success': False, 'message': '服务器内部错误'}), 500

@app.route('/api/admin/add_license', methods=['POST'])
def add_license():
    """添加卡密API (管理员功能)"""
    try:
        data = request.get_json()
        key_code = data.get('key_code')
        project_code = data.get('project_code', 'DEFAULT')
        expire_days = data.get('expire_days', 30)
        max_uses = data.get('max_uses', -1)

        if not key_code:
            return jsonify({'success': False, 'message': '缺少卡密'}), 400

        # 计算过期时间
        expire_date = None
        if expire_days > 0:
            expire_date = (datetime.now() + timedelta(days=expire_days)).isoformat()

        # 添加到数据库
        conn = sqlite3.connect(CONFIG['DATABASE'])
        cursor = conn.cursor()

        try:
            cursor.execute('''
                INSERT INTO license_keys (key_code, project_code, expire_date, max_uses)
                VALUES (?, ?, ?, ?)
            ''', (key_code, project_code, expire_date, max_uses))

            conn.commit()
            conn.close()

            return jsonify({
                'success': True,
                'message': '卡密添加成功',
                'key_code': key_code,
                'project_code': project_code,
                'expire_date': expire_date
            })

        except sqlite3.IntegrityError:
            conn.close()
            return jsonify({'success': False, 'message': '卡密已存在'}), 400

    except Exception as e:
        print(f"添加卡密异常: {e}")
        return jsonify({'success': False, 'message': '服务器内部错误'}), 500

@app.route('/api/status', methods=['GET'])
def server_status():
    """服务器状态检查"""
    return jsonify({
        'status': 'running',
        'timestamp': int(time.time()),
        'version': '1.0'
    })

if __name__ == '__main__':
    print("启动Python加密验证服务器...")
    print("=" * 50)
    print("API端点:")
    print("  POST /api/auth - 验证卡密")
    print("  POST /api/admin/add_license - 添加卡密")
    print("  GET  /api/status - 服务器状态")
    print("=" * 50)
    
    # 启动服务器
    app.run(host='0.0.0.0', port=5000, debug=False)
