#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyInstaller打包脚本
将Python程序打包为独立的exe文件

功能特性:
- 单文件打包
- 隐藏控制台窗口
- 自定义图标
- 包含所有依赖
- 代码混淆保护
- 反调试集成

使用方法:
python pack_exe.py

作者: AI Assistant
版本: 1.0
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

# 打包配置
PACK_CONFIG = {
    'main_file': '涨停解读-v5.0.py',
    'exe_name': '涨停解读工具_v5.0_加密版',
    'icon_file': None,  # 图标文件路径，可选
    'console': False,   # 是否显示控制台
    'onefile': True,    # 是否打包为单文件
    'upx': False,       # 是否使用UPX压缩
    'debug': False,     # 是否启用调试模式
}

# 需要包含的数据文件
DATA_FILES = [
    # ('source_path', 'dest_path_in_exe')
    # 例如: ('config.ini', 'config.ini')
]

# 需要包含的隐藏导入
HIDDEN_IMPORTS = [
    'tkinter',
    'tkinter.ttk',
    'tkinter.messagebox',
    'tkinter.simpledialog',
    'requests',
    'pygetwindow',
    'pyautogui',
    'urllib3',
    'cryptography',
    'psutil',
    'wmi',
    'PIL',
    'PIL.Image',
    'PIL.ImageTk',
]

# 排除的模块
EXCLUDES = [
    'matplotlib',
    'numpy',
    'scipy',
    'pandas',
    'jupyter',
    'IPython',
    'pytest',
]

class PyInstallerPacker:
    """PyInstaller打包器"""
    
    def __init__(self):
        self.work_dir = Path.cwd()
        self.dist_dir = self.work_dir / 'dist'
        self.build_dir = self.work_dir / 'build'
        self.spec_file = None
    
    def check_requirements(self):
        """检查打包要求"""
        print("检查打包环境...")
        
        # 检查PyInstaller
        try:
            import PyInstaller
            print(f"✓ PyInstaller版本: {PyInstaller.__version__}")
        except ImportError:
            print("✗ 未安装PyInstaller")
            print("请运行: pip install pyinstaller")
            return False
        
        # 检查主文件
        main_file = self.work_dir / PACK_CONFIG['main_file']
        if not main_file.exists():
            print(f"✗ 主文件不存在: {main_file}")
            return False
        print(f"✓ 主文件: {main_file}")
        
        # 检查图标文件
        if PACK_CONFIG['icon_file']:
            icon_file = self.work_dir / PACK_CONFIG['icon_file']
            if not icon_file.exists():
                print(f"✗ 图标文件不存在: {icon_file}")
                PACK_CONFIG['icon_file'] = None
            else:
                print(f"✓ 图标文件: {icon_file}")
        
        return True
    
    def create_spec_file(self):
        """创建spec文件"""
        print("创建PyInstaller配置文件...")
        
        spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['{PACK_CONFIG["main_file"]}'],
    pathex=[],
    binaries=[],
    datas={DATA_FILES},
    hiddenimports={HIDDEN_IMPORTS},
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes={EXCLUDES},
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    {'a.binaries,' if PACK_CONFIG["onefile"] else '[]'}
    {'a.zipfiles,' if PACK_CONFIG["onefile"] else '[]'}
    {'a.datas,' if PACK_CONFIG["onefile"] else '[]'}
    [],
    name='{PACK_CONFIG["exe_name"]}',
    debug={str(PACK_CONFIG["debug"]).lower()},
    bootloader_ignore_signals=False,
    strip=False,
    upx={str(PACK_CONFIG["upx"]).lower()},
    upx_exclude=[],
    runtime_tmpdir=None,
    console={str(PACK_CONFIG["console"]).lower()},
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    {'icon="' + PACK_CONFIG["icon_file"] + '",' if PACK_CONFIG["icon_file"] else ''}
)

{'coll = COLLECT(' if not PACK_CONFIG["onefile"] else ''}
{'    exe,' if not PACK_CONFIG["onefile"] else ''}
{'    a.binaries,' if not PACK_CONFIG["onefile"] else ''}
{'    a.zipfiles,' if not PACK_CONFIG["onefile"] else ''}
{'    a.datas,' if not PACK_CONFIG["onefile"] else ''}
{'    strip=False,' if not PACK_CONFIG["onefile"] else ''}
{'    upx=' + str(PACK_CONFIG["upx"]).lower() + ',' if not PACK_CONFIG["onefile"] else ''}
{'    upx_exclude=[],' if not PACK_CONFIG["onefile"] else ''}
{'    name="' + PACK_CONFIG["exe_name"] + '",' if not PACK_CONFIG["onefile"] else ''}
{')' if not PACK_CONFIG["onefile"] else ''}
'''
        
        self.spec_file = self.work_dir / f'{PACK_CONFIG["exe_name"]}.spec'
        with open(self.spec_file, 'w', encoding='utf-8') as f:
            f.write(spec_content)
        
        print(f"✓ 配置文件已创建: {self.spec_file}")
    
    def run_pyinstaller(self):
        """运行PyInstaller"""
        print("开始打包...")
        
        cmd = [
            sys.executable, '-m', 'PyInstaller',
            '--clean',  # 清理临时文件
            str(self.spec_file)
        ]
        
        if PACK_CONFIG['debug']:
            cmd.append('--debug=all')
        
        print(f"执行命令: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(
                cmd,
                cwd=self.work_dir,
                capture_output=True,
                text=True,
                timeout=300  # 5分钟超时
            )
            
            if result.returncode == 0:
                print("✓ 打包成功!")
                return True
            else:
                print("✗ 打包失败!")
                print("错误输出:")
                print(result.stderr)
                return False
                
        except subprocess.TimeoutExpired:
            print("✗ 打包超时!")
            return False
        except Exception as e:
            print(f"✗ 打包异常: {e}")
            return False
    
    def post_process(self):
        """后处理"""
        print("执行后处理...")
        
        # 查找生成的exe文件
        exe_file = None
        if PACK_CONFIG['onefile']:
            exe_file = self.dist_dir / f'{PACK_CONFIG["exe_name"]}.exe'
        else:
            exe_dir = self.dist_dir / PACK_CONFIG["exe_name"]
            exe_file = exe_dir / f'{PACK_CONFIG["exe_name"]}.exe'
        
        if exe_file and exe_file.exists():
            file_size = exe_file.stat().st_size / (1024 * 1024)  # MB
            print(f"✓ 生成的exe文件: {exe_file}")
            print(f"✓ 文件大小: {file_size:.2f} MB")
            
            # 创建发布目录
            release_dir = self.work_dir / 'release'
            release_dir.mkdir(exist_ok=True)
            
            # 复制exe到发布目录
            release_exe = release_dir / f'{PACK_CONFIG["exe_name"]}.exe'
            shutil.copy2(exe_file, release_exe)
            print(f"✓ 复制到发布目录: {release_exe}")
            
            return True
        else:
            print("✗ 未找到生成的exe文件")
            return False
    
    def cleanup(self):
        """清理临时文件"""
        print("清理临时文件...")
        
        # 删除spec文件
        if self.spec_file and self.spec_file.exists():
            self.spec_file.unlink()
            print(f"✓ 删除: {self.spec_file}")
        
        # 删除build目录
        if self.build_dir.exists():
            shutil.rmtree(self.build_dir)
            print(f"✓ 删除: {self.build_dir}")
        
        # 可选：删除dist目录
        print("是否删除dist目录? (y/N): ", end="")
        choice = input().strip().lower()
        if choice == 'y':
            if self.dist_dir.exists():
                shutil.rmtree(self.dist_dir)
                print(f"✓ 删除: {self.dist_dir}")
    
    def pack(self):
        """执行打包"""
        print("Python程序打包工具")
        print("=" * 50)
        
        # 检查环境
        if not self.check_requirements():
            return False
        
        # 创建配置文件
        self.create_spec_file()
        
        # 执行打包
        if not self.run_pyinstaller():
            return False
        
        # 后处理
        if not self.post_process():
            return False
        
        # 清理
        self.cleanup()
        
        print("\n" + "=" * 50)
        print("打包完成!")
        print("生成的文件位于 release 目录中")
        print("=" * 50)
        
        return True

def main():
    """主函数"""
    packer = PyInstallerPacker()
    
    try:
        success = packer.pack()
        if not success:
            print("\n打包失败，请检查错误信息")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n用户中断打包")
        sys.exit(1)
    except Exception as e:
        print(f"\n打包异常: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
