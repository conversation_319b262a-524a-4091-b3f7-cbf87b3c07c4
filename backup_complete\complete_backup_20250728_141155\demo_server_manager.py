#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证服务器管理工具
提供卡密管理、用户管理、日志查看等功能

功能特性:
- 卡密生成和管理
- 用户验证记录查看
- 服务器状态监控
- 数据库备份恢复

作者: 股中掘金
版本: 1.0
"""

import os
import sys
import sqlite3
import json
import time
import random
import string
from datetime import datetime, timedelta
from pathlib import Pathcmd

class ServerManager:
    """服务器管理器"""
    
    def __init__(self, db_path='license.db'):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """初始化数据库"""
        if not os.path.exists(self.db_path):
            print(f"数据库文件不存在: {self.db_path}")
            print("请先启动验证服务器创建数据库")
            return
        
        print(f"连接数据库: {self.db_path}")
    
    def generate_key_code(self, length=16):
        """生成卡密"""
        chars = string.ascii_uppercase + string.digits
        key_parts = []
        
        # 生成4段，每段4个字符
        for _ in range(4):
            part = ''.join(random.choices(chars, k=4))
            key_parts.append(part)
        
        return '-'.join(key_parts)
    
    def add_license(self, key_code=None, project_code="DEFAULT", expire_days=30, max_uses=-1):
        """添加卡密"""
        if not key_code:
            key_code = self.generate_key_code()

        expire_date = None
        if expire_days > 0:
            expire_date = (datetime.now() + timedelta(days=expire_days)).isoformat()

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO license_keys (key_code, project_code, expire_date, max_uses)
                VALUES (?, ?, ?, ?)
            ''', (key_code, project_code, expire_date, max_uses))

            conn.commit()
            conn.close()

            print(f"✓ 卡密添加成功:")
            print(f"  卡密: {key_code}")
            print(f"  项目: {project_code}")
            print(f"  过期时间: {expire_date or '永不过期'}")
            print(f"  使用次数限制: {max_uses if max_uses > 0 else '无限制'}")

            return key_code

        except sqlite3.IntegrityError:
            print(f"✗ 卡密已存在: {key_code}")
            return None
        except Exception as e:
            print(f"✗ 添加卡密失败: {e}")
            return None
    
    def list_licenses(self, limit=20):
        """列出卡密"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT key_code, project_code, hwid, status, expire_date, created_at,
                       last_used, use_count, max_uses
                FROM license_keys
                ORDER BY created_at DESC
                LIMIT ?
            ''', (limit,))
            
            results = cursor.fetchall()
            conn.close()
            
            if not results:
                print("没有找到卡密记录")
                return
            
            print(f"\n卡密列表 (最近{len(results)}条):")
            print("-" * 100)
            print(f"{'卡密':<20} {'项目':<12} {'状态':<6} {'HWID':<16} {'过期时间':<12} {'使用次数':<8} {'最后使用':<12}")
            print("-" * 110)

            for row in results:
                key_code, project_code, hwid, status, expire_date, created_at, last_used, use_count, max_uses = row
                
                status_text = "有效" if status == 1 else "禁用"
                project_short = (project_code or "DEFAULT")[:10]
                hwid_short = (hwid[:14] + "...") if hwid and len(hwid) > 14 else (hwid or "未绑定")
                expire_short = expire_date[:10] if expire_date else "永不过期"
                use_info = f"{use_count}/{max_uses if max_uses > 0 else '∞'}"
                last_used_short = last_used[:10] if last_used else "从未使用"

                print(f"{key_code:<20} {project_short:<12} {status_text:<6} {hwid_short:<16} {expire_short:<12} {use_info:<8} {last_used_short:<12}")
            
        except Exception as e:
            print(f"✗ 查询卡密失败: {e}")
    
    def disable_license(self, key_code):
        """禁用卡密"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE license_keys SET status = 0 WHERE key_code = ?
            ''', (key_code,))
            
            if cursor.rowcount > 0:
                conn.commit()
                print(f"✓ 卡密已禁用: {key_code}")
            else:
                print(f"✗ 卡密不存在: {key_code}")
            
            conn.close()
            
        except Exception as e:
            print(f"✗ 禁用卡密失败: {e}")
    
    def enable_license(self, key_code):
        """启用卡密"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE license_keys SET status = 1 WHERE key_code = ?
            ''', (key_code,))
            
            if cursor.rowcount > 0:
                conn.commit()
                print(f"✓ 卡密已启用: {key_code}")
            else:
                print(f"✗ 卡密不存在: {key_code}")
            
            conn.close()
            
        except Exception as e:
            print(f"✗ 启用卡密失败: {e}")
    
    def view_auth_logs(self, limit=50):
        """查看验证日志"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT key_code, project_code, hwid, ip_address, result, error_msg, timestamp
                FROM auth_logs
                ORDER BY timestamp DESC
                LIMIT ?
            ''', (limit,))


            results = cursor.fetchall()
            conn.close()
            
            if not results:
                print("没有找到验证日志")
                return
            
            print(f"\n验证日志 (最近{len(results)}条):")
            print("-" * 120)
            print(f"{'时间':<20} {'卡密':<20} {'项目':<12} {'结果':<8} {'IP地址':<15} {'错误信息':<25}")
            print("-" * 125)

            for row in results:
                key_code, project_code, hwid, ip_address, result, error_msg, timestamp = row
                
                time_short = timestamp[:19] if timestamp else ""
                project_short = (project_code or "DEFAULT")[:10]
                result_text = "成功" if result == "success" else "失败"
                error_short = (error_msg[:23] + "...") if error_msg and len(error_msg) > 23 else (error_msg or "")

                print(f"{time_short:<20} {key_code:<20} {project_short:<12} {result_text:<8} {ip_address:<15} {error_short:<25}")
            
        except Exception as e:
            print(f"✗ 查询日志失败: {e}")
    
    def get_statistics(self):
        """获取统计信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 卡密统计
            cursor.execute('SELECT COUNT(*) FROM license_keys')
            total_keys = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM license_keys WHERE status = 1')
            active_keys = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM license_keys WHERE hwid IS NOT NULL')
            bound_keys = cursor.fetchone()[0]
            
            # 验证统计
            cursor.execute('SELECT COUNT(*) FROM auth_logs')
            total_auths = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM auth_logs WHERE result = "success"')
            success_auths = cursor.fetchone()[0]
            
            cursor.execute('''
                SELECT COUNT(*) FROM auth_logs 
                WHERE timestamp > datetime('now', '-24 hours')
            ''')
            today_auths = cursor.fetchone()[0]
            
            conn.close()
            
            print("\n📊 服务器统计信息:")
            print("-" * 40)
            print(f"总卡密数量: {total_keys}")
            print(f"有效卡密: {active_keys}")
            print(f"已绑定卡密: {bound_keys}")
            print(f"总验证次数: {total_auths}")
            print(f"成功验证次数: {success_auths}")
            print(f"今日验证次数: {today_auths}")
            
            if total_auths > 0:
                success_rate = (success_auths / total_auths) * 100
                print(f"验证成功率: {success_rate:.1f}%")
            
        except Exception as e:
            print(f"✗ 获取统计信息失败: {e}")
    
    def batch_generate_keys(self, count=10, expire_days=30):
        """批量生成卡密"""
        print(f"批量生成 {count} 个卡密...")
        
        generated_keys = []
        for i in range(count):
            key_code = self.add_license(expire_days=expire_days)
            if key_code:
                generated_keys.append(key_code)
            
            if (i + 1) % 10 == 0:
                print(f"已生成 {i + 1}/{count} 个卡密")
        
        # 保存到文件
        if generated_keys:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"keys_batch_{timestamp}.txt"
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"批量生成卡密 - {datetime.now()}\n")
                f.write(f"过期天数: {expire_days}\n")
                f.write("-" * 50 + "\n")
                for key in generated_keys:
                    f.write(f"{key}\n")
            
            print(f"✓ 卡密已保存到文件: {filename}")
        
        return generated_keys

def main():
    """主函数"""
    manager = ServerManager()
    
    while True:
        print("\n" + "=" * 50)
        print("验证服务器管理工具")
        print("=" * 50)
        print("1. 添加卡密")
        print("2. 批量生成卡密")
        print("3. 查看卡密列表")
        print("4. 禁用卡密")
        print("5. 启用卡密")
        print("6. 查看验证日志")
        print("7. 查看统计信息")
        print("0. 退出")
        print("-" * 50)
        
        try:
            choice = input("请选择操作 (0-7): ").strip()
            
            if choice == '0':
                print("退出管理工具")
                break
            elif choice == '1':
                expire_days = input("过期天数 (默认30天): ").strip()
                expire_days = int(expire_days) if expire_days else 30
                manager.add_license(expire_days=expire_days)
            elif choice == '2':
                count = input("生成数量 (默认10个): ").strip()
                count = int(count) if count else 10
                expire_days = input("过期天数 (默认30天): ").strip()
                expire_days = int(expire_days) if expire_days else 30
                manager.batch_generate_keys(count, expire_days)
            elif choice == '3':
                limit = input("显示数量 (默认20条): ").strip()
                limit = int(limit) if limit else 20
                manager.list_licenses(limit)
            elif choice == '4':
                key_code = input("请输入要禁用的卡密: ").strip()
                if key_code:
                    manager.disable_license(key_code)
            elif choice == '5':
                key_code = input("请输入要启用的卡密: ").strip()
                if key_code:
                    manager.enable_license(key_code)
            elif choice == '6':
                limit = input("显示数量 (默认50条): ").strip()
                limit = int(limit) if limit else 50
                manager.view_auth_logs(limit)
            elif choice == '7':
                manager.get_statistics()
            else:
                print("无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n\n用户中断，退出程序")
            break
        except Exception as e:
            print(f"操作失败: {e}")

if __name__ == "__main__":
    main()
