#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整构建脚本
Cython编译 + PyInstaller打包一键完成

使用方法:
python complete_build.py

作者: 股中掘金
版本: 1.0
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
from datetime import datetime

class CompleteBuildTool:
    """完整构建工具"""
    
    def __init__(self):
        self.project_dir = Path.cwd()
        self.backup_dir = self.project_dir / 'backup_complete'
        
    def create_backup(self):
        """创建完整备份"""
        print("创建项目备份...")
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = self.backup_dir / f"complete_backup_{timestamp}"
        backup_path.mkdir(parents=True, exist_ok=True)
        
        # 备份所有Python文件
        for py_file in self.project_dir.glob("*.py"):
            if py_file.name not in ['complete_build.py', 'cython_build.py']:
                shutil.copy2(py_file, backup_path)
                print(f"  备份: {py_file.name}")
        
        print(f"✓ 备份完成: {backup_path}")
        return backup_path
    
    def run_cython_build(self):
        """运行Cython编译"""
        print("\n" + "=" * 50)
        print("第一步: Cython编译")
        print("=" * 50)
        
        cython_script = self.project_dir / 'cython_build.py'
        if not cython_script.exists():
            print("✗ 未找到cython_build.py")
            return False
        
        try:
            result = subprocess.run([
                sys.executable, str(cython_script)
            ], timeout=300)
            
            if result.returncode == 0:
                print("✓ Cython编译完成")
                return True
            else:
                print("✗ Cython编译失败")
                return False
                
        except subprocess.TimeoutExpired:
            print("✗ Cython编译超时")
            return False
        except Exception as e:
            print(f"✗ Cython编译异常: {e}")
            return False
    
    def run_pyinstaller_build(self):
        """运行PyInstaller打包"""
        print("\n" + "=" * 50)
        print("第二步: PyInstaller打包")
        print("=" * 50)
        
        # 准备主文件
        original_file = self.project_dir / '涨停解读-v5.0.py'
        main_file = self.project_dir / 'zhangting_main.py'
        
        if not main_file.exists() and original_file.exists():
            shutil.copy2(original_file, main_file)
            print(f"✓ 复制主文件: {main_file}")
        elif not main_file.exists():
            print("✗ 未找到主文件")
            return False
        
        # 创建release目录
        release_dir = self.project_dir / 'release'
        release_dir.mkdir(exist_ok=True)
        
        # PyInstaller命令
        cmd = [
            sys.executable, '-m', 'PyInstaller',
            '--onefile',
            '--noconsole',
            '--name=涨停解读工具_v5.0_加密版',
            '--distpath=release',
            '--clean',
            
            # 排除大型模块
            '--exclude-module=matplotlib',
            '--exclude-module=numpy',
            '--exclude-module=scipy',
            '--exclude-module=pandas',
            '--exclude-module=jupyter',
            '--exclude-module=IPython',
            '--exclude-module=pytest',
            
            # 必要的隐藏导入
            '--hidden-import=tkinter',
            '--hidden-import=tkinter.ttk',
            '--hidden-import=tkinter.messagebox',
            '--hidden-import=tkinter.simpledialog',
            '--hidden-import=requests',
            '--hidden-import=pygetwindow',
            '--hidden-import=pyautogui',
            '--hidden-import=urllib3',
            '--hidden-import=cryptography',
            '--hidden-import=psutil',
            '--hidden-import=PIL',
            '--hidden-import=PIL.Image',
            '--hidden-import=PIL.ImageTk',
            
            str(main_file)
        ]
        
        print("开始PyInstaller打包...")
        print("这可能需要几分钟时间，请耐心等待...")
        
        try:
            result = subprocess.run(cmd, timeout=600)
            
            if result.returncode == 0:
                exe_file = release_dir / '涨停解读工具_v5.0_加密版.exe'
                
                if exe_file.exists():
                    file_size = exe_file.stat().st_size / (1024 * 1024)
                    print(f"\n✓ 打包成功!")
                    print(f"✓ 生成文件: {exe_file}")
                    print(f"✓ 文件大小: {file_size:.2f} MB")
                    return True
                else:
                    print("✗ 未找到生成的exe文件")
                    return False
            else:
                print("✗ 打包失败")
                return False
                
        except subprocess.TimeoutExpired:
            print("✗ 打包超时")
            return False
        except Exception as e:
            print(f"✗ 打包异常: {e}")
            return False
    
    def cleanup_build_files(self):
        """清理构建文件"""
        print("\n清理构建文件...")
        
        cleanup_items = [
            'build', '__pycache__', '*.spec', 'zhangting_main.py'
        ]
        
        for item in cleanup_items:
            for path in self.project_dir.glob(item):
                if path.is_file():
                    path.unlink()
                    print(f"  删除文件: {path.name}")
                elif path.is_dir():
                    shutil.rmtree(path)
                    print(f"  删除目录: {path.name}")
        
        print("✓ 清理完成")
    
    def build(self):
        """执行完整构建"""
        print("Python项目完整加密构建工具")
        print("=" * 60)
        print("流程: 备份 → Cython编译 → PyInstaller打包")
        print("=" * 60)
        
        try:
            # 1. 创建备份
            backup_path = self.create_backup()
            
            # 2. Cython编译
            if not self.run_cython_build():
                print("\n❌ 构建失败：Cython编译错误")
                return False
            
            # 3. PyInstaller打包
            if not self.run_pyinstaller_build():
                print("\n❌ 构建失败：PyInstaller打包错误")
                return False
            
            # 4. 清理临时文件
            self.cleanup_build_files()
            
            print("\n" + "=" * 60)
            print("🎉 完整构建成功!")
            print(f"备份位置: {backup_path}")
            print(f"exe文件: {self.project_dir / 'release'}")
            print("\n安全特性:")
            print("✓ 核心模块已编译为C扩展")
            print("✓ 反调试保护已启用")
            print("✓ 网络验证已集成")
            print("✓ 项目代码隔离")
            print("=" * 60)
            
            return True
            
        except Exception as e:
            print(f"\n构建异常: {e}")
            return False

def main():
    """主函数"""
    # 检查必要文件
    required_files = [
        '涨停解读-v5.0.py', 
        'auth_client.py', 
        'anti_debug.py',
        'license_dialog.py',
        'cython_build.py'
    ]
    
    missing_files = []
    for file_name in required_files:
        if not Path(file_name).exists():
            missing_files.append(file_name)
    
    if missing_files:
        print("错误：缺少必要文件:")
        for file_name in missing_files:
            print(f"  - {file_name}")
        return False
    
    # 检查依赖
    try:
        import Cython
        print(f"Cython版本: {Cython.__version__}")
    except ImportError:
        print("错误：未安装Cython，请运行: pip install Cython")
        return False
    
    try:
        import PyInstaller
        print(f"PyInstaller版本: {PyInstaller.__version__}")
    except ImportError:
        print("错误：未安装PyInstaller，请运行: pip install PyInstaller")
        return False
    
    # 执行构建
    builder = CompleteBuildTool()
    
    try:
        success = builder.build()
        return success
    except KeyboardInterrupt:
        print("\n用户中断构建")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎉 完整加密构建完成!")
        print("现在你拥有了一个高度安全的exe文件")
    else:
        print("\n❌ 构建失败")
    
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
