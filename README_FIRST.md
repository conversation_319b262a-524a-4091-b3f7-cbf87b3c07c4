# Python加密验证工具套件 - 使用指南

## 🚀 快速开始

### 1. 验证服务器
```bash
# 启动验证服务器
python auth_server.py

# 使用图形界面管理卡密（推荐）
python server_ui.py

# 或使用命令行管理
python demo_server_manager.py
```

### 2. 运行程序
```bash
# 运行加密版主程序（推荐）
python ZhangTing-v5.1.py

# 或运行旧版本
python 涨停解读-v5.0.py

# 或运行已打包的exe文件
release/涨停解读工具_v5.0_精简版.exe
```

### 3. 测试验证系统
```bash
# 测试验证模块
python test_auth.py

# 测试简化版本
python ZhangTing-v5.1-test.py
```

### 4. 编译和打包
```bash
# Cython编译（提高安全性）
python cython_build.py

# 完整构建（编译+打包）
python complete_build.py
```

## 📁 文件说明

### 核心文件
- `ZhangTing-v5.1.py` - 加密版主程序（推荐）⭐
- `涨停解读-v5.0.py` - 旧版主程序
- `GetDayZhangTing.py` - API接口模块
- `auth_client.py` - 客户端验证模块
- `auth_server.py` - 验证服务器

### 安全模块
- `anti_debug.py` - 反调试模块
- `license_dialog.py` - 卡密输入界面

### 管理工具
- `server_ui.py` - 服务器图形界面管理工具（推荐）
- `demo_server_manager.py` - 服务器命令行管理工具

### 编译工具
- `cython_build.py` - Cython编译脚本
- `complete_build.py` - 完整构建脚本

### 测试工具
- `test_auth.py` - 验证系统测试
- `ZhangTing-v5.1-test.py` - 简化测试版本

### 发布文件
- `release/涨停解读工具_v5.0_精简版.exe` - 优化版exe文件（28.3MB）

### 文档
- `加密方案总结.md` - 完整的加密方案文档

## 🔧 验证系统使用

### 服务器端
1. 启动验证服务器：`python auth_server.py`
2. 添加卡密：`python demo_server_manager.py` 选择选项1
3. 服务器默认在 `http://localhost:5000` 运行

### 客户端
1. 运行程序时会自动请求输入卡密
2. 首次使用会绑定硬件ID
3. 验证成功后程序正常运行

## 🛠️ 常见问题

### 验证失败
- 确保验证服务器正在运行
- 检查卡密是否正确
- 检查是否已绑定其他设备

### 打包问题
- 中文文件名可能导致打包失败
- 请参考手动打包指南

## 📞 技术支持

如有问题，请联系：
- QQ: 2267617536

## 📝 注意事项

1. 请勿修改验证模块
2. 请勿尝试绕过验证
3. 请在使用前修改默认密钥
