#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cython编译脚本
用于将Python代码编译为C扩展，提供代码保护

功能特性:
- 编译Python文件为.pyd/.so文件
- 移除原始.py文件
- 支持批量编译
- 自动处理依赖关系

使用方法:
python setup_cython.py build_ext --inplace

作者: AI Assistant
版本: 1.0
"""

import os
import sys
import shutil
from distutils.core import setup
from Cython.Build import cythonize
from Cython.Distutils import build_ext
import glob

# 需要编译的Python文件列表
COMPILE_FILES = [
    "auth_client.py",
    "GetDayZhangTing.py",
    # "涨停解读-v5.0.py"  # 主文件暂时不编译，便于调试
]

# 排除编译的文件
EXCLUDE_FILES = [
    "setup_cython.py",
    "auth_server.py",  # 服务器端文件不编译
    "build_tool.py",
    "pack_exe.py"
]

class CustomBuildExt(build_ext):
    """自定义编译类"""
    
    def run(self):
        # 执行编译
        build_ext.run(self)
        
        # 编译完成后清理
        self.cleanup_files()
    
    def cleanup_files(self):
        """清理编译产生的临时文件"""
        print("正在清理临时文件...")
        
        # 删除.c文件
        for c_file in glob.glob("*.c"):
            try:
                os.remove(c_file)
                print(f"删除: {c_file}")
            except:
                pass
        
        # 删除build目录
        if os.path.exists("build"):
            try:
                shutil.rmtree("build")
                print("删除: build目录")
            except:
                pass
        
        print("清理完成")

def get_compile_files():
    """获取需要编译的文件列表"""
    files_to_compile = []
    
    # 如果指定了文件列表，使用指定的文件
    if COMPILE_FILES:
        for file in COMPILE_FILES:
            if os.path.exists(file) and file not in EXCLUDE_FILES:
                files_to_compile.append(file)
    else:
        # 否则编译所有.py文件
        for py_file in glob.glob("*.py"):
            if py_file not in EXCLUDE_FILES:
                files_to_compile.append(py_file)
    
    return files_to_compile

def backup_original_files(files):
    """备份原始文件"""
    backup_dir = "backup_original"
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)
    
    for file in files:
        if os.path.exists(file):
            backup_path = os.path.join(backup_dir, file)
            shutil.copy2(file, backup_path)
            print(f"备份: {file} -> {backup_path}")

def remove_original_files(files):
    """移除原始Python文件"""
    print("\n是否删除原始.py文件? (y/N): ", end="")
    choice = input().strip().lower()
    
    if choice == 'y':
        for file in files:
            if os.path.exists(file):
                try:
                    os.remove(file)
                    print(f"删除原始文件: {file}")
                except Exception as e:
                    print(f"删除失败 {file}: {e}")
    else:
        print("保留原始文件")

def main():
    """主函数"""
    print("Cython代码编译工具")
    print("=" * 50)
    
    # 检查Cython是否安装
    try:
        import Cython
        print(f"Cython版本: {Cython.__version__}")
    except ImportError:
        print("错误: 未安装Cython")
        print("请运行: pip install Cython")
        return
    
    # 获取要编译的文件
    files_to_compile = get_compile_files()
    
    if not files_to_compile:
        print("没有找到需要编译的Python文件")
        return
    
    print(f"\n将编译以下文件:")
    for file in files_to_compile:
        print(f"  - {file}")
    
    # 备份原始文件
    print(f"\n备份原始文件...")
    backup_original_files(files_to_compile)
    
    # 设置编译参数
    compiler_directives = {
        'language_level': 3,
        'boundscheck': False,
        'wraparound': False,
        'initializedcheck': False,
        'cdivision': True,
        'embedsignature': False,  # 不嵌入函数签名，增加混淆
    }
    
    # 执行编译
    print(f"\n开始编译...")
    try:
        setup(
            name="EncryptedModules",
            cmdclass={'build_ext': CustomBuildExt},
            ext_modules=cythonize(
                files_to_compile,
                compiler_directives=compiler_directives,
                build_dir="build"
            ),
            zip_safe=False,
        )
        
        print("\n编译成功!")
        
        # 显示编译结果
        print("\n编译产物:")
        for ext in ['*.pyd', '*.so']:
            for compiled_file in glob.glob(ext):
                print(f"  - {compiled_file}")
        
        # 询问是否删除原始文件
        remove_original_files(files_to_compile)
        
    except Exception as e:
        print(f"\n编译失败: {e}")
        return

if __name__ == "__main__":
    # 检查命令行参数
    if len(sys.argv) < 2:
        sys.argv.extend(['build_ext', '--inplace'])
    
    main()
