#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
卡密输入对话框
提供美观的卡密输入界面

功能特性:
- 美观的UI设计
- 自动保存选项
- 联系信息显示
- 输入验证

作者: 股中掘金
版本: 1.0
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os

class LicenseDialog:
    """卡密输入对话框"""
    
    def __init__(self, parent=None, project_name="软件", auto_save_enabled=True):
        self.parent = parent
        self.project_name = project_name
        self.auto_save_enabled = auto_save_enabled
        self.result = None
        self.auto_save = False
        self.dialog = None
        
    def show_dialog(self):
        """显示对话框"""
        # 创建对话框窗口
        if self.parent:
            self.dialog = tk.Toplevel(self.parent)
            self.dialog.transient(self.parent)
        else:
            self.dialog = tk.Tk()
        
        self.dialog.title(f"{self.project_name} - 卡密验证")
        self.dialog.geometry("450x350")
        self.dialog.resizable(False, False)
        
        # 居中显示
        self.center_window()
        
        # 设置窗口属性
        self.dialog.grab_set()
        self.dialog.focus_set()
        
        # 创建界面
        self.create_interface()
        
        # 运行对话框
        self.dialog.mainloop()
        
        return self.result, self.auto_save
    
    def center_window(self):
        """窗口居中"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_interface(self):
        """创建界面"""
        # 主框架
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(
            main_frame, 
            text=f"{self.project_name} 卡密验证",
            font=("Arial", 16, "bold")
        )
        title_label.pack(pady=(0, 20))
        
        # 说明文字
        info_frame = ttk.LabelFrame(main_frame, text="使用说明", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 15))
        
        info_text = f"""欢迎使用 {self.project_name}！

请输入您的卡密以继续使用本软件。
如果您还没有卡密，请联系我们购买。

购买卡密联系QQ：2267617536"""
        
        info_label = ttk.Label(info_frame, text=info_text, justify=tk.LEFT)
        info_label.pack()
        
        # 卡密输入框
        input_frame = ttk.LabelFrame(main_frame, text="卡密输入", padding="10")
        input_frame.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Label(input_frame, text="请输入卡密:").pack(anchor=tk.W)
        
        self.key_var = tk.StringVar()
        self.key_entry = ttk.Entry(
            input_frame, 
            textvariable=self.key_var,
            font=("Consolas", 11),
            width=40,
            show="*"  # 隐藏输入内容
        )
        self.key_entry.pack(pady=(5, 10), fill=tk.X)
        self.key_entry.focus_set()
        
        # 显示/隐藏密码按钮
        show_frame = ttk.Frame(input_frame)
        show_frame.pack(fill=tk.X)
        
        self.show_var = tk.BooleanVar()
        show_check = ttk.Checkbutton(
            show_frame,
            text="显示卡密",
            variable=self.show_var,
            command=self.toggle_password_visibility
        )
        show_check.pack(anchor=tk.W)
        
        # 自动保存选项
        if self.auto_save_enabled:
            save_frame = ttk.LabelFrame(main_frame, text="保存选项", padding="10")
            save_frame.pack(fill=tk.X, pady=(0, 15))
            
            self.save_var = tk.BooleanVar()
            save_check = ttk.Checkbutton(
                save_frame,
                text="自动保存卡密（下次启动时无需重新输入）",
                variable=self.save_var
            )
            save_check.pack(anchor=tk.W)
            
            # 说明文字
            save_info = ttk.Label(
                save_frame,
                text="注意：卡密将加密保存在本地，仅在当前设备有效",
                font=("Arial", 9),
                foreground="gray"
            )
            save_info.pack(anchor=tk.W, pady=(5, 0))
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        # 确定按钮
        ok_button = ttk.Button(
            button_frame,
            text="验证卡密",
            command=self.on_ok,
            width=12
        )
        ok_button.pack(side=tk.RIGHT, padx=(5, 0))
        
        # 取消按钮
        cancel_button = ttk.Button(
            button_frame,
            text="取消",
            command=self.on_cancel,
            width=12
        )
        cancel_button.pack(side=tk.RIGHT)
        
        # 联系信息
        contact_frame = ttk.Frame(main_frame)
        contact_frame.pack(fill=tk.X, pady=(15, 0))
        
        contact_label = ttk.Label(
            contact_frame,
            text="技术支持QQ：2267617536",
            font=("Arial", 9),
            foreground="blue"
        )
        contact_label.pack()
        
        # 绑定回车键
        self.dialog.bind('<Return>', lambda e: self.on_ok())
        self.dialog.bind('<Escape>', lambda e: self.on_cancel())
        
        # 窗口关闭事件
        self.dialog.protocol("WM_DELETE_WINDOW", self.on_cancel)
    
    def toggle_password_visibility(self):
        """切换密码显示/隐藏"""
        if self.show_var.get():
            self.key_entry.config(show="")
        else:
            self.key_entry.config(show="*")
    
    def on_ok(self):
        """确定按钮点击"""
        key_code = self.key_var.get().strip()
        
        if not key_code:
            messagebox.showwarning("警告", "请输入卡密！")
            self.key_entry.focus_set()
            return
        
        # 简单的卡密格式验证
        if len(key_code) < 8:
            messagebox.showwarning("警告", "卡密格式不正确！")
            self.key_entry.focus_set()
            return
        
        self.result = key_code
        if self.auto_save_enabled:
            self.auto_save = self.save_var.get()
        
        self.dialog.destroy()
    
    def on_cancel(self):
        """取消按钮点击"""
        self.result = None
        self.auto_save = False
        self.dialog.destroy()

def show_license_dialog(parent=None, project_name="软件", auto_save_enabled=True):
    """显示卡密输入对话框"""
    dialog = LicenseDialog(parent, project_name, auto_save_enabled)
    return dialog.show_dialog()

# 测试代码
if __name__ == "__main__":
    # 测试对话框
    key_code, auto_save = show_license_dialog(project_name="涨停解读工具")
    
    if key_code:
        print(f"输入的卡密: {key_code}")
        print(f"自动保存: {auto_save}")
    else:
        print("用户取消了输入")
