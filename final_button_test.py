#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终按钮测试脚本
测试修复后的按钮显示效果
"""

import tkinter as tk
from tkinter import ttk, messagebox

def create_test_dialog():
    """创建测试对话框"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    dialog = tk.Toplevel(root)
    dialog.title("添加卡密 - 修复测试")
    dialog.geometry("400x350")
    dialog.grab_set()
    
    # 表单
    ttk.Label(dialog, text="项目代码:").pack(pady=5)
    project_var = tk.StringVar(value="DEFAULT")
    project_entry = ttk.Entry(dialog, textvariable=project_var, width=40)
    project_entry.pack(pady=5)
    
    ttk.Label(dialog, text="卡密 (留空自动生成):").pack(pady=5)
    key_entry = ttk.Entry(dialog, width=40)
    key_entry.pack(pady=5)
    
    ttk.Label(dialog, text="有效期 (天数):").pack(pady=5)
    expire_var = tk.StringVar(value="30")
    expire_entry = ttk.Entry(dialog, textvariable=expire_var, width=20)
    expire_entry.pack(pady=5)
    
    ttk.Label(dialog, text="使用次数限制 (-1为无限制):").pack(pady=5)
    uses_var = tk.StringVar(value="-1")
    uses_entry = ttk.Entry(dialog, textvariable=uses_var, width=20)
    uses_entry.pack(pady=5)
    
    def add_license():
        messagebox.showinfo("测试成功", "确认按钮工作正常!\n按钮文本显示正确!")
        dialog.destroy()
        root.quit()
    
    def cancel_action():
        messagebox.showinfo("测试成功", "取消按钮工作正常!\n按钮文本显示正确!")
        dialog.destroy()
        root.quit()
    
    # 按钮框架
    button_frame = ttk.Frame(dialog)
    button_frame.pack(pady=20)
    
    # 使用修复后的按钮样式
    confirm_btn = tk.Button(
        button_frame, 
        text="确认", 
        command=add_license, 
        width=12, 
        height=1,
        font=("Arial", 9),
        relief="raised",
        bd=2
    )
    confirm_btn.pack(side=tk.LEFT, padx=5)
    
    cancel_btn = tk.Button(
        button_frame, 
        text="取消", 
        command=cancel_action, 
        width=12, 
        height=1,
        font=("Arial", 9),
        relief="raised",
        bd=2
    )
    cancel_btn.pack(side=tk.LEFT, padx=5)
    
    # 添加说明文字
    info_label = ttk.Label(dialog, text="请检查按钮是否显示文字", foreground="blue")
    info_label.pack(pady=10)
    
    print("✓ 测试对话框已创建")
    print("✓ 请检查按钮是否显示'确认'和'取消'文字")
    print("✓ 点击任意按钮测试功能")
    
    root.mainloop()

def main():
    """主函数"""
    print("最终按钮修复测试")
    print("=" * 30)
    print("这个测试将显示修复后的添加卡密对话框")
    print("请检查按钮是否正确显示文字")
    print()
    
    input("按回车键开始测试...")
    create_test_dialog()
    
    print("\n测试完成!")

if __name__ == "__main__":
    main()
