#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试UI界面的按钮显示
"""

import tkinter as tk
from tkinter import ttk, messagebox

def test_add_license_dialog():
    """测试添加卡密对话框"""
    root = tk.Tk()
    root.title("测试主窗口")
    root.geometry("300x200")
    
    def show_add_dialog():
        """显示添加卡密对话框"""
        dialog = tk.Toplevel(root)
        dialog.title("添加卡密")
        dialog.geometry("450x350")
        dialog.transient(root)
        dialog.grab_set()
        dialog.resizable(False, False)
        
        # 居中显示
        dialog.geometry("+%d+%d" % (root.winfo_rootx() + 50, root.winfo_rooty() + 50))
        
        # 主框架
        main_frame = ttk.Frame(dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 表单
        ttk.Label(main_frame, text="项目代码:").pack(pady=(0, 5), anchor=tk.W)
        project_var = tk.StringVar(value="DEFAULT")
        project_entry = ttk.Entry(main_frame, textvariable=project_var, width=40)
        project_entry.pack(pady=(0, 15), fill=tk.X)

        ttk.Label(main_frame, text="卡密 (留空自动生成):").pack(pady=(0, 5), anchor=tk.W)
        key_entry = ttk.Entry(main_frame, width=40)
        key_entry.pack(pady=(0, 15), fill=tk.X)

        ttk.Label(main_frame, text="有效期 (天数):").pack(pady=(0, 5), anchor=tk.W)
        expire_var = tk.StringVar(value="30")
        expire_entry = ttk.Entry(main_frame, textvariable=expire_var, width=20)
        expire_entry.pack(pady=(0, 15), fill=tk.X)

        ttk.Label(main_frame, text="使用次数限制 (-1为无限制):").pack(pady=(0, 5), anchor=tk.W)
        uses_var = tk.StringVar(value="-1")
        uses_entry = ttk.Entry(main_frame, textvariable=uses_var, width=20)
        uses_entry.pack(pady=(0, 15), fill=tk.X)
        
        def add_license():
            messagebox.showinfo("测试", "按钮点击成功！")
            dialog.destroy()

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=(20, 0), fill=tk.X)

        # 创建按钮（使用ttk.Button确保更好的兼容性）
        confirm_btn = ttk.Button(
            button_frame,
            text="确认",
            command=add_license,
            width=15
        )
        confirm_btn.pack(side=tk.RIGHT, padx=(5, 0))

        cancel_btn = ttk.Button(
            button_frame,
            text="取消",
            command=dialog.destroy,
            width=15
        )
        cancel_btn.pack(side=tk.RIGHT, padx=(0, 5))
    
    # 主窗口按钮
    ttk.Button(root, text="测试添加卡密对话框", command=show_add_dialog).pack(pady=50)
    
    root.mainloop()

if __name__ == "__main__":
    test_add_license_dialog()
