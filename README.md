# Python加密验证工具套件

一套完整的Python程序加密验证解决方案，包含网络验证、代码混淆、反调试保护和exe打包功能。

## 🚀 功能特性

### 核心功能
- **网络卡密验证**: 基于Flask的验证服务器，支持卡密+HWID绑定
- **代码混淆保护**: 使用Cython将Python代码编译为C扩展
- **反调试检测**: 多层次反调试和反分析保护
- **一键打包**: PyInstaller自动打包为独立exe文件
- **可复用框架**: 可快速应用到其他Python项目

### 安全特性
- AES加密通信
- 请求签名验证
- 硬件ID绑定
- 时间戳防重放
- 虚拟机检测
- 调试器检测
- 进程监控

## 📁 文件结构

```
├── auth_server.py          # 验证服务器
├── auth_client.py          # 客户端验证模块
├── anti_debug.py           # 高级反调试模块
├── setup_cython.py        # Cython编译脚本
├── pack_exe.py            # PyInstaller打包脚本
├── build_tool.py          # 一键构建工具
├── 涨停解读-v5.0.py        # 示例项目（已集成验证）
├── GetDayZhangTing.py     # API接口模块
├── requirements_server.txt # 服务器依赖
└── README.md              # 说明文档
```

## 🛠️ 安装依赖

### 服务器端依赖
```bash
pip install -r requirements_server.txt
```

### 客户端依赖
```bash
pip install requests cryptography psutil pygetwindow pyautogui
pip install Cython PyInstaller  # 用于编译和打包
```

### Windows特定依赖
```bash
pip install wmi  # 用于虚拟机检测
```

## 🚀 快速开始

### 1. 启动验证服务器

```bash
# 修改auth_server.py中的SECRET_KEY
python auth_server.py
```

服务器将在 `http://localhost:5000` 启动

### 2. 添加测试卡密

```bash
curl -X POST http://localhost:5000/api/admin/add_license \
  -H "Content-Type: application/json" \
  -d '{"key_code": "TEST-KEY-123456", "expire_days": 30}'
```

### 3. 测试验证功能

```bash
python auth_client.py
```

### 4. 运行示例项目

```bash
python 涨停解读-v5.0.py
```

## 🔧 使用方法

### 方法一：一键构建（推荐）

```bash
# 在项目目录下运行
python build_tool.py

# 或指定项目目录
python build_tool.py /path/to/your/project
```

### 方法二：分步构建

#### 1. 代码编译
```bash
python setup_cython.py build_ext --inplace
```

#### 2. 打包exe
```bash
python pack_exe.py
```

## 📝 配置说明

### 验证配置

在你的Python项目中添加验证配置：

```python
# 验证配置
AUTH_CONFIG = {
    'SERVER_URL': 'http://your-server.com:5000',  # 验证服务器地址
    'SECRET_KEY': 'your-secret-key-change-this',   # 与服务器保持一致
    'DEMO_MODE': False,  # 是否启用演示模式
}
```

### 构建配置

创建 `build_config.json` 文件：

```json
{
  "project_name": "MyProject",
  "main_file": "main.py",
  "auth_config": {
    "server_url": "http://localhost:5000",
    "secret_key": "your-secret-key-change-this",
    "demo_mode": false
  },
  "cython_config": {
    "compile_files": [],
    "exclude_files": ["build_tool.py"],
    "remove_original": true
  },
  "pack_config": {
    "exe_name": "MyProject_Encrypted",
    "console": false,
    "onefile": true
  }
}
```

## 🔐 验证流程

1. **客户端启动** → 反调试检测
2. **获取HWID** → 生成硬件指纹
3. **输入卡密** → 用户输入验证码
4. **加密通信** → AES加密 + 签名验证
5. **服务器验证** → 检查卡密、HWID、过期时间
6. **返回结果** → 验证成功/失败
7. **程序运行** → 验证通过后正常运行

## 🛡️ 安全机制

### 客户端保护
- Cython代码编译
- 反调试检测
- 虚拟机检测
- 进程监控
- 时间检测

### 通信保护
- AES-256加密
- HMAC签名验证
- 时间戳防重放
- 请求频率限制

### 服务器保护
- SQLite数据库
- 卡密状态管理
- 使用次数限制
- 详细日志记录

## 📊 API接口

### 验证接口
```
POST /api/auth
Content-Type: application/json

{
  "encrypted_data": "...",
  "signature": "...",
  "timestamp": **********
}
```

### 管理接口
```
POST /api/admin/add_license
Content-Type: application/json

{
  "key_code": "TEST-KEY-123456",
  "expire_days": 30,
  "max_uses": -1
}
```

## 🔍 故障排除

### 常见问题

1. **验证失败**
   - 检查服务器是否运行
   - 确认SECRET_KEY一致
   - 检查网络连接

2. **编译失败**
   - 安装Visual Studio Build Tools (Windows)
   - 检查Cython版本
   - 确认Python版本兼容

3. **打包失败**
   - 检查PyInstaller版本
   - 确认所有依赖已安装
   - 检查文件路径

### 调试模式

启用调试模式：
```python
AUTH_CONFIG['DEMO_MODE'] = True  # 跳过验证
PACK_CONFIG['debug'] = True      # 打包调试
```

## 📄 许可证

本项目仅供学习和研究使用，请勿用于商业用途。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 📞 联系方式

- QQ: **********
- 邮箱: [<EMAIL>]

---

**注意**: 请在使用前修改所有默认密钥和配置，确保系统安全。
