#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
开盘啦API - 个股涨停解读数据获取工具
GetDayZhangTing.py

功能：
- 获取个股历史涨停数据
- 支持批量查询多只股票
- 无需登录认证，公开接口
- 完整的错误处理和重试机制

作者：基于开盘啦APP逆向分析
日期：2025-01-15
版本：1.0
"""

import requests
import json
import urllib3
import time
from datetime import datetime
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from typing import Dict, List, Optional, Union

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class KaiPanLaAPI:
    """开盘啦API客户端"""
    
    def __init__(self, device_id: str = None):
        """
        初始化API客户端
        
        Args:
            device_id: 设备ID，可选，使用默认值
        """
        self.base_url = "https://apphis.longhuvip.com/w1/api/index.php"
        self.device_id = device_id or "558526c1-dc18-463e-b915-453a8993cf56"
        self.session = self._create_session()
    
    def _create_session(self) -> requests.Session:
        """创建带重试机制的会话"""
        session = requests.Session()
        
        # 配置重试策略
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        return session

    def get_today_limit_reason(self, stock_id: str, date: str = None) -> Dict:
        """
        获取指定日期的涨停原因（主要用于当日涨停）

        Args:
            stock_id: 股票代码，如 '002724', '000001'
            date: 查询日期，格式 'YYYY-MM-DD'，默认为今天

        Returns:
            dict: 包含当日涨停原因的字典
        """
        if date is None:
            date = datetime.now().strftime("%Y-%m-%d")

        headers = {
            'Host': 'apphis.longhuvip.com',
            'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8',
            'Connection': 'keep-alive',
            'Accept': '*/*',
            'User-Agent': 'lhb/5.20.7 (com.kaipanla.www; build:0; iOS 16.1.1) Alamofire/4.9.1',
            'Accept-Language': 'zh-Hans-CN;q=1.0',
            'Accept-Encoding': 'gzip;q=1.0, compress;q=0.5'
        }

        data = {
            'Date': date,
            'PhoneOSNew': '2',
            'StockID': stock_id,
            'VerSion': '********',
            'a': 'KLineZhangTingReason',
            'apiv': 'w41',
            'c': 'HisLimitResumption'
        }

        try:
            response = self.session.post(
                self.base_url,
                headers=headers,
                data=data,
                verify=False,
                timeout=30
            )

            response.raise_for_status()
            result = response.json()

            # 添加查询信息到结果中
            result['StockID'] = stock_id
            result['QueryDate'] = date

            return result

        except requests.exceptions.RequestException as e:
            return {
                "error": "请求失败",
                "details": str(e),
                "StockID": stock_id,
                "QueryDate": date
            }
        except json.JSONDecodeError as e:
            return {
                "error": "JSON解析失败",
                "details": str(e),
                "StockID": stock_id,
                "QueryDate": date
            }
        except Exception as e:
            return {
                "error": "未知错误",
                "details": str(e),
                "StockID": stock_id,
                "QueryDate": date
            }

    def get_stock_limit_history(self, stock_id: str) -> Dict:
        """
        获取个股历史涨停数据
        
        Args:
            stock_id: 股票代码，如 '002724', '000001'
        
        Returns:
            dict: 包含涨停历史数据的字典
        """
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'User-Agent': 'Dalvik/2.1.0 (Linux; U; Android 12; ALN-AL00 Build/HUAWEIALN-AL00)',
            'Host': 'apphis.longhuvip.com',
            'Connection': 'Keep-Alive',
            'Accept-Encoding': 'gzip'
        }
        
        data = {
            'a': 'GetDayZhangTing',           # 接口动作
            'st': '25',                      # 状态参数
            'apiv': 'w41',                   # API版本
            'c': 'HisLimitResumption',       # 控制器：历史涨停复盘
            'StockID': stock_id,             # 股票代码
            'PhoneOSNew': '1',               # 手机系统标识
            'UserID': '0',                   # 用户ID（无需真实值）
            'DeviceID': self.device_id,      # 设备ID
            'VerSion': '********',          # 应用版本
            'Token': '0',                    # 认证令牌（无需真实值）
            'Index': '0'                     # 分页索引
        }
        
        try:
            response = self.session.post(
                self.base_url,
                headers=headers,
                data=data,
                verify=False,  # 禁用SSL验证
                timeout=30
            )
            
            response.raise_for_status()
            result = response.json()
            
            # 添加查询的股票代码到结果中
            if 'StockID' not in result:
                result['StockID'] = stock_id
            
            return result
            
        except requests.exceptions.RequestException as e:
            return {
                "error": "请求失败",
                "details": str(e),
                "StockID": stock_id
            }
        except json.JSONDecodeError as e:
            return {
                "error": "JSON解析失败",
                "details": str(e),
                "StockID": stock_id
            }
        except Exception as e:
            return {
                "error": "未知错误",
                "details": str(e),
                "StockID": stock_id
            }
    
    def batch_query_stocks(self, stock_list: List[str], delay: float = 0.5) -> Dict[str, Dict]:
        """
        批量查询多只股票的涨停数据
        
        Args:
            stock_list: 股票代码列表
            delay: 请求间隔时间（秒），避免频率过高
        
        Returns:
            dict: 以股票代码为key的结果字典
        """
        results = {}
        
        for i, stock_id in enumerate(stock_list):
            print(f"正在查询 {stock_id} ({i+1}/{len(stock_list)})...")
            
            result = self.get_stock_limit_history(stock_id)
            results[stock_id] = result
            
            # 添加延时，避免请求过于频繁
            if i < len(stock_list) - 1:
                time.sleep(delay)
        
        return results

    def get_complete_limit_data(self, stock_id: str, include_today: bool = True) -> Dict:
        """
        获取完整的涨停数据（历史 + 当日）

        Args:
            stock_id: 股票代码
            include_today: 是否包含当日涨停数据

        Returns:
            dict: 合并后的完整涨停数据
        """
        # 获取历史涨停数据
        history_data = self.get_stock_limit_history(stock_id)

        if "error" in history_data:
            return history_data

        # 如果不需要包含当日数据，直接返回历史数据
        if not include_today:
            return history_data

        # 获取当日涨停数据
        today_data = self.get_today_limit_reason(stock_id)

        # 检查当日是否有涨停
        if ("error" not in today_data and
            "info" in today_data and
            today_data["info"].get("reason") and
            today_data["info"]["reason"] != False):

            # 构造当日涨停记录
            today_record = {
                "Date": today_data.get("QueryDate", datetime.now().strftime("%Y-%m-%d")),
                "Reason": today_data["info"]["reason"],
                "SCLT": today_data["info"].get("autoLt", ""),
                "TiCai": today_data["info"].get("tiCai", ""),
                "Group": today_data["info"].get("group", ""),
                "IsToday": True  # 标记为当日数据
            }

            # 将当日记录插入到历史记录的最前面
            history_list = history_data.get("List", [])

            # 检查是否已经存在今天的记录
            today_date = today_record["Date"]
            existing_today_index = -1
            for i, item in enumerate(history_list):
                if item.get("Date") == today_date:
                    existing_today_index = i
                    break

            if existing_today_index >= 0:
                # 如果历史记录中已有今天的记录，用当日涨停API的详细信息替换
                # 并标记为当日涨停
                history_list[existing_today_index] = today_record
                history_data["HasTodayLimit"] = True
                history_data["TodayLimitInfo"] = today_data["info"]
            else:
                # 如果历史记录中没有今天的记录，插入到最前面
                history_list.insert(0, today_record)
                history_data["List"] = history_list
                history_data["HasTodayLimit"] = True
                history_data["TodayLimitInfo"] = today_data["info"]
        else:
            history_data["HasTodayLimit"] = False
            history_data["TodayLimitInfo"] = None

        return history_data

def format_limit_data(data: Dict) -> str:
    """
    格式化涨停数据为可读字符串

    Args:
        data: API返回的数据

    Returns:
        str: 格式化后的字符串
    """
    if "error" in data:
        return f"❌ 查询失败: {data['error']}"

    stock_id = data.get('StockID', 'Unknown')
    limit_list = data.get('List', [])
    has_today = data.get('HasTodayLimit', False)

    if not limit_list:
        return f"📊 {stock_id}: 无涨停记录"

    result = f"📊 {stock_id}: {len(limit_list)} 条涨停记录"
    if has_today:
        result += " (含当日涨停)"
    result += "\n"

    # 显示最近3条记录
    for i, item in enumerate(limit_list[:3]):
        date = item.get('Date', 'N/A')
        reason = item.get('Reason', 'N/A')
        sclt = item.get('SCLT', '')
        is_today = item.get('IsToday', False)

        if is_today:
            result += f"🔥 {date} - {reason}"
            if sclt:
                result += f" ({sclt})"
            result += " [当日涨停]"
        else:
            result += f"  {i+1}. {date} - {reason}"
            if sclt:
                result += f" ({sclt})"
        result += "\n"

    if len(limit_list) > 3:
        result += f"  ... 还有 {len(limit_list) - 3} 条记录\n"

    return result

def main():
    """主函数 - 演示用法"""
    print("🚀 开盘啦个股涨停解读工具 (升级版)")
    print("=" * 60)

    # 创建API客户端
    api = KaiPanLaAPI()

    # 测试当日涨停查询
    print("\n🔥 当日涨停查询测试:")
    print("-" * 30)

    test_stock = "002246"  # 使用有当日涨停的股票
    today_result = api.get_today_limit_reason(test_stock)
    print(f"当日涨停查询结果: {test_stock}")
    if "error" not in today_result and today_result.get("info", {}).get("reason"):
        info = today_result["info"]
        print(f"✅ 当日涨停原因: {info['reason']}")
        print(f"✅ 龙虎榜位置: {info.get('autoLt', '无')}")
    else:
        print("❌ 当日无涨停")

    # 测试完整数据查询（历史+当日）
    print(f"\n📊 完整涨停数据查询测试:")
    print("-" * 30)

    complete_result = api.get_complete_limit_data(test_stock)
    print(format_limit_data(complete_result))

    # 测试单只股票历史查询
    print(f"\n📈 历史涨停查询测试:")
    print("-" * 30)

    test_stock2 = "002724"
    result = api.get_stock_limit_history(test_stock2)
    print(format_limit_data(result))
    
    # 测试批量查询
    print("\n📊 批量查询测试:")
    print("-" * 30)
    
    test_stocks = [
        "000001",  # 平安银行
        "000002",  # 万科A
        "600036",  # 招商银行
        "300015",  # 爱尔眼科
        "002724"   # 海洋王
    ]
    
    batch_results = api.batch_query_stocks(test_stocks)
    
    print("\n📋 批量查询结果汇总:")
    print("-" * 30)
    
    for stock_id, data in batch_results.items():
        print(format_limit_data(data))
    
    # 统计信息
    total_records = 0
    success_count = 0
    
    for data in batch_results.values():
        if "error" not in data:
            success_count += 1
            total_records += len(data.get('List', []))
    
    print(f"\n📈 统计信息:")
    print(f"查询股票数: {len(test_stocks)}")
    print(f"成功查询: {success_count}")
    print(f"涨停记录总数: {total_records}")
    
    print("\n" + "=" * 60)
    print("🎯 重要发现:")
    print("✅ 历史涨停数据接口无需登录认证")
    print("✅ Token和UserID可以设置为0")
    print("✅ 支持批量查询任意股票代码")
    print("✅ 数据包含详细的涨停原因和日期")
    print("=" * 60)

def save_to_json(data: Dict, filename: str = None) -> str:
    """
    保存数据到JSON文件

    Args:
        data: 要保存的数据
        filename: 文件名，可选

    Returns:
        str: 保存的文件路径
    """
    if filename is None:
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        filename = f"kaipanla_zhangting_{timestamp}.json"

    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

    return filename

def analyze_limit_reasons(batch_results: Dict[str, Dict]) -> Dict:
    """
    分析涨停原因统计

    Args:
        batch_results: 批量查询结果

    Returns:
        dict: 涨停原因统计
    """
    reason_stats = {}

    for _, data in batch_results.items():
        if "error" in data:
            continue

        for item in data.get('List', []):
            reason = item.get('Reason', '').split('；')[0]  # 取第一个原因
            if reason:
                reason_stats[reason] = reason_stats.get(reason, 0) + 1

    # 按频次排序
    return dict(sorted(reason_stats.items(), key=lambda x: x[1], reverse=True))

def interactive_query():
    """交互式查询模式"""
    api = KaiPanLaAPI()

    print("\n🔍 交互式查询模式")
    print("输入股票代码查询涨停数据，输入 'quit' 退出")
    print("-" * 40)

    while True:
        stock_input = input("\n请输入股票代码 (如 002724): ").strip()

        if stock_input.lower() in ['quit', 'exit', 'q']:
            print("👋 退出查询模式")
            break

        if not stock_input:
            continue

        print(f"正在查询 {stock_input}...")
        result = api.get_stock_limit_history(stock_input)
        print(format_limit_data(result))

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        # 命令行模式
        if sys.argv[1] == "-i" or sys.argv[1] == "--interactive":
            interactive_query()
        elif sys.argv[1] == "-h" or sys.argv[1] == "--help":
            print("开盘啦个股涨停解读工具")
            print("用法:")
            print("  python GetDayZhangTing.py           # 运行演示")
            print("  python GetDayZhangTing.py -i       # 交互式查询")
            print("  python GetDayZhangTing.py -h       # 显示帮助")
        else:
            # 直接查询指定股票
            stock_code = sys.argv[1]
            api = KaiPanLaAPI()
            result = api.get_stock_limit_history(stock_code)
            print(format_limit_data(result))
    else:
        # 默认演示模式
        main()
