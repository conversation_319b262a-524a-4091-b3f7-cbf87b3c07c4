#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# 设置编码环境变量，确保在Windows下正常运行
import os
import sys

# 设置环境变量
os.environ['PYTHONIOENCODING'] = 'utf-8'

# 在Windows环境下设置控制台编码
if sys.platform == 'win32':
    try:
        # 尝试设置控制台编码为UTF-8
        import locale
        locale.setlocale(locale.LC_ALL, 'C.UTF-8')
    except:
        try:
            # 备用方案：设置为系统默认编码
            import locale
            locale.setlocale(locale.LC_ALL, '')
        except:
            # 如果都失败，继续使用默认设置
            pass

# 导入验证模块
try:
    from auth_client import AuthClient, AntiDebug
    AUTH_ENABLED = True
except ImportError:
    print("警告: 验证模块未找到，程序将以演示模式运行")
    AUTH_ENABLED = False
"""
异动解读工具 - v5.0 加密版
结合开盘啦API，为大智慧和通达信软件提供涨停解读功能

v5.0 加密版新增功能：
- 网络卡密验证系统
- 硬件ID绑定保护
- 反调试检测
- 加密通信保护
- 支持通达信软件窗口识别
- 软件选择UI（大智慧/通达信）
- 启动弹窗（含赞赏码和交流群）
- 简化底部状态栏
- 优化用户体验

v4.0 功能：
- 支持获取当日涨停信息（实时）
- 自动合并当日涨停与历史涨停数据
- 当日涨停特殊标记显示（🔥标识）
- 完善的错误处理和重试机制

原有功能：
- 自动检查并安装依赖库
- 自动获取当前查看股票的涨停历史
- 在软件界面中显示涨停解读信息
- 支持快捷键触发和自动刷新
- 提供简洁的涨停信息展示
- 去掉UI界面左右外边框
- 删除"第X次"和"详情"描述
- 支持字体大小调整
- 显示股票代码+股票名称

作者：基于开盘啦API开发
日期：2025-07-24
版本：5.0 (加密验证版)
"""

# 验证配置
AUTH_CONFIG = {
    'SERVER_URL': 'http://localhost:5000',  # 验证服务器地址
    'SECRET_KEY': 'your-secret-key-change-this',  # 与服务器保持一致
    'DEMO_MODE': not AUTH_ENABLED,  # 演示模式（无验证模块时启用）
}

import sys
import subprocess
import importlib

def safe_print(text):
    """安全的打印函数，处理编码问题"""
    try:
        print(text)
    except (UnicodeEncodeError, UnicodeDecodeError, AttributeError):
        try:
            # 移除emoji字符，只保留基本文本
            import re
            clean_text = re.sub(r'[^\x00-\x7F]+', '', str(text))
            print(clean_text)
        except:
            # 最后的备用方案：只打印ASCII字符
            try:
                ascii_text = str(text).encode('ascii', 'ignore').decode('ascii')
                print(ascii_text)
            except:
                # 如果所有方法都失败，静默处理
                pass

def perform_license_validation():
    """执行卡密验证"""
    if AUTH_CONFIG['DEMO_MODE']:
        safe_print("演示模式：跳过验证")
        return True

    try:
        # 反调试检测
        if AntiDebug.check_debugger():
            safe_print("检测到调试器，程序退出")
            sys.exit(1)

        if AntiDebug.check_vm():
            safe_print("检测到虚拟机环境，程序退出")
            sys.exit(1)

        # 创建验证客户端
        auth_client = AuthClient(
            AUTH_CONFIG['SERVER_URL'],
            AUTH_CONFIG['SECRET_KEY']
        )

        safe_print(f"硬件ID: {auth_client.get_hwid()}")

        # 获取卡密
        import tkinter as tk
        from tkinter import simpledialog, messagebox

        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口

        key_code = simpledialog.askstring(
            "卡密验证",
            "请输入您的卡密:",
            show='*'  # 隐藏输入内容
        )

        if not key_code:
            messagebox.showerror("错误", "未输入卡密，程序退出")
            root.destroy()
            return False

        # 执行验证
        safe_print("正在验证卡密...")
        success, message = auth_client.validate_license(key_code)

        root.destroy()

        if success:
            safe_print(f"验证成功: {message}")
            return True
        else:
            safe_print(f"验证失败: {message}")
            return False

    except Exception as e:
        safe_print(f"验证异常: {e}")
        return False

def check_and_install_dependencies():
    """检查并自动安装依赖库"""
    safe_print("正在检查依赖库...")

    # 定义需要的依赖库
    dependencies = {
        'requests': 'requests',
        'pygetwindow': 'pygetwindow',
        'pyautogui': 'pyautogui',
        'urllib3': 'urllib3'
    }

    # 如果启用验证，添加验证相关依赖
    if AUTH_ENABLED:
        dependencies.update({
            'cryptography': 'cryptography',
            'psutil': 'psutil'
        })

    missing_packages = []

    # 检查每个依赖库
    for package_name, pip_name in dependencies.items():
        try:
            importlib.import_module(package_name)
            safe_print(f"{package_name} 已安装")
        except ImportError:
            safe_print(f"{package_name} 未安装")
            missing_packages.append(pip_name)

    # 如果有缺失的包，自动安装
    if missing_packages:
        safe_print(f"\n需要安装以下依赖库: {', '.join(missing_packages)}")
        safe_print("正在自动安装...")

        for package in missing_packages:
            try:
                safe_print(f"正在安装 {package}...")
                subprocess.run([
                    sys.executable, '-m', 'pip', 'install', package
                ], capture_output=True, text=True, check=True)
                safe_print(f"{package} 安装成功")
            except subprocess.CalledProcessError as e:
                safe_print(f"{package} 安装失败: {e}")
                safe_print(f"错误输出: {e.stderr}")
                return False
            except Exception as e:
                safe_print(f"安装 {package} 时发生未知错误: {e}")
                return False

        safe_print("所有依赖库安装完成！")
    else:
        safe_print("所有依赖库已就绪！")

    return True

def main_program():
    """主程序逻辑"""
    # 导入所需模块
    import tkinter as tk
    from tkinter import ttk, messagebox
    import threading
    import time
    import re
    import pygetwindow as gw
    import requests
    from GetDayZhangTing import KaiPanLaAPI

    class ZhangTingJieDu:
        """涨停解读工具主类"""
        
        def __init__(self):
            self.api = KaiPanLaAPI()
            self.current_stock = None
            self.current_stock_name = None  # 添加股票名称存储
            self.auto_refresh = False
            self.refresh_interval = 5  # 秒
            self.window = None
            self.auto_monitor_running = False
            self.auto_monitor_thread = None
            self.last_window_title = ""
            self.font_size = 10  # 添加字体大小控制
            self.stock_name_cache = {}  # 添加股票名称缓存

            self.setup_gui()

            # v5.0 新增：显示启动弹窗
            self.show_startup_dialog()
        
        def setup_gui(self):
            """设置GUI界面"""
            self.window = tk.Tk()

            # 根据验证状态设置标题
            if AUTH_CONFIG['DEMO_MODE']:
                self.window.title("异动解读工具v5.0 (演示版)")
            else:
                self.window.title("异动解读工具v5.0 (已授权)")

            self.window.geometry("800x600")
            self.window.configure(bg='#1e1e1e')  # 深色主题

            # 设置窗口置顶
            self.window.attributes('-topmost', True)

            # v5.0 新增：初始化软件选择变量
            self.dazhihui_enabled = tk.BooleanVar()
            self.tongdaxin_enabled = tk.BooleanVar()

            # 创建主框架 - 完全无边框样式
            main_frame = ttk.Frame(self.window)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=0, pady=0)
            
            # 第一行：主要控制面板
            control_frame1 = ttk.Frame(main_frame)
            control_frame1.pack(fill=tk.X, pady=(5, 2))

            # 股票代码输入
            ttk.Label(control_frame1, text="股票代码:").pack(side=tk.LEFT, padx=(0, 5))
            self.stock_entry = ttk.Entry(control_frame1, width=10)
            self.stock_entry.pack(side=tk.LEFT, padx=(0, 10))
            self.stock_entry.bind('<Return>', self.on_manual_query)

            # 主要按钮组
            ttk.Button(control_frame1, text="查询", command=self.on_manual_query).pack(side=tk.LEFT, padx=(0, 5))
            ttk.Button(control_frame1, text="自动获取", command=self.auto_get_stock).pack(side=tk.LEFT, padx=(0, 5))
            ttk.Button(control_frame1, text="测试检测", command=self.test_window_detection).pack(side=tk.LEFT, padx=(0, 5))
            ttk.Button(control_frame1, text="刷新", command=self.refresh_data).pack(side=tk.LEFT, padx=(0, 5))

            # 状态标签
            self.status_label = ttk.Label(control_frame1, text="就绪", foreground="green")
            self.status_label.pack(side=tk.RIGHT)

            # 第二行：自动功能和字体控制
            control_frame2 = ttk.Frame(main_frame)
            control_frame2.pack(fill=tk.X, pady=(2, 5))

            # 自动刷新控制
            self.auto_refresh_var = tk.BooleanVar()
            ttk.Checkbutton(control_frame2, text="自动刷新", variable=self.auto_refresh_var,
                           command=self.toggle_auto_refresh).pack(side=tk.LEFT, padx=(0, 10))

            # 自动监控控制
            self.auto_monitor_var = tk.BooleanVar()
            ttk.Checkbutton(control_frame2, text="自动监控", variable=self.auto_monitor_var,
                           command=self.toggle_auto_monitor).pack(side=tk.LEFT, padx=(0, 10))

            # v5.0 新增：软件选择控制
            ttk.Label(control_frame2, text="软件选择:").pack(side=tk.LEFT, padx=(10, 5))
            ttk.Checkbutton(control_frame2, text="大智慧", variable=self.dazhihui_enabled,
                           command=self.on_software_selection_change).pack(side=tk.LEFT, padx=(0, 5))
            ttk.Checkbutton(control_frame2, text="通达信", variable=self.tongdaxin_enabled,
                           command=self.on_software_selection_change).pack(side=tk.LEFT, padx=(0, 10))

            # 字体大小控制
            ttk.Label(control_frame2, text="字体大小:").pack(side=tk.LEFT, padx=(10, 5))
            ttk.Button(control_frame2, text="A+", command=self.increase_font_size, width=3).pack(side=tk.LEFT, padx=(0, 2))
            ttk.Button(control_frame2, text="A-", command=self.decrease_font_size, width=3).pack(side=tk.LEFT, padx=(0, 10))

            # 功能说明标签
            info_text = "自动刷新: 每5秒刷新数据 | 自动监控: 检测软件窗口变化 | 软件选择: 选择要监控的软件"
            ttk.Label(control_frame2, text=info_text, foreground="gray", font=("Arial", 8)).pack(side=tk.RIGHT)
            
            # 信息显示区域 - 减少内边距，去掉标题边框
            info_frame = ttk.Frame(main_frame)
            info_frame.pack(fill=tk.BOTH, expand=True, padx=0, pady=0)
            
            # 股票基本信息 - 减少下边距
            basic_frame = ttk.Frame(info_frame)
            basic_frame.pack(fill=tk.X, pady=(0, 5))
            
            self.stock_info_label = ttk.Label(basic_frame, text="股票: 未选择", font=("Arial", 12, "bold"))
            self.stock_info_label.pack(side=tk.LEFT)
            
            self.limit_count_label = ttk.Label(basic_frame, text="涨停次数: 0", font=("Arial", 10))
            self.limit_count_label.pack(side=tk.RIGHT)
            
            # 涨停记录显示 - 使用普通Text控件，去掉滚动条，完全无边距
            self.text_area = tk.Text(
                info_frame,
                wrap=tk.WORD,
                height=25,
                bg='#2d2d2d',
                fg='#ffffff',
                font=("Consolas", self.font_size),
                borderwidth=0,           # 去掉边框
                highlightthickness=0,    # 去掉高亮边框
                relief='flat',           # 平面样式
                padx=2,                  # 极小的内边距
                pady=2
            )
            self.text_area.pack(fill=tk.BOTH, expand=True, padx=0, pady=0)

            # 绑定鼠标滚轮事件
            self.text_area.bind("<MouseWheel>", self._on_mousewheel)
            self.text_area.bind("<Button-4>", self._on_mousewheel)  # Linux
            self.text_area.bind("<Button-5>", self._on_mousewheel)  # Linux

            # 设置文本样式
            self.update_text_styles()

            # 底部作者信息状态栏
            author_frame = ttk.Frame(main_frame)
            author_frame.pack(fill=tk.X, side=tk.BOTTOM, pady=(5, 0))

            # 作者信息标签（居中显示）
            if AUTH_CONFIG['DEMO_MODE']:
                author_text = "作者QQ：2267617536 | 演示版本 - 请联系作者获取正式版"
                author_color = "#ff6666"
            else:
                author_text = "作者QQ：2267617536 | 已授权版本"
                author_color = "#66ff66"

            author_label = ttk.Label(
                author_frame,
                text=author_text,
                font=("Arial", 10),
                foreground=author_color
            )
            author_label.pack(pady=5)

        def update_text_styles(self):
            """更新文本样式"""
            self.text_area.tag_configure("header", foreground="#00ff00", font=("Arial", self.font_size + 1, "bold"))
            self.text_area.tag_configure("date", foreground="#ffff00", font=("Arial", self.font_size, "bold"))
            self.text_area.tag_configure("reason", foreground="#ffffff", font=("Arial", self.font_size))
            self.text_area.tag_configure("detail", foreground="#cccccc", font=("Arial", self.font_size - 1))
            self.text_area.tag_configure("separator", foreground="#666666")

        def increase_font_size(self):
            """增大字体"""
            if self.font_size < 20:
                self.font_size += 1
                self.text_area.config(font=("Consolas", self.font_size))
                self.update_text_styles()

        def decrease_font_size(self):
            """减小字体"""
            if self.font_size > 8:
                self.font_size -= 1
                self.text_area.config(font=("Consolas", self.font_size))
                self.update_text_styles()

        def _on_mousewheel(self, event):
            """处理鼠标滚轮事件"""
            # Windows鼠标滚轮事件
            if event.num == 5 or event.delta < 0:
                self.text_area.yview_scroll(1, "units")
            elif event.num == 4 or event.delta > 0:
                self.text_area.yview_scroll(-1, "units")
            return "break"  # 阻止默认行为

        def get_stock_name_by_code(self, stock_code):
            """通过股票代码获取股票名称"""
            # 检查缓存
            if stock_code in self.stock_name_cache:
                return self.stock_name_cache[stock_code]

            # 方案1: 尝试腾讯财经API
            stock_name = self._get_name_from_tencent(stock_code)
            if stock_name:
                self.stock_name_cache[stock_code] = stock_name
                return stock_name

            # 方案2: 尝试东方财富API
            stock_name = self._get_name_from_eastmoney(stock_code)
            if stock_name:
                self.stock_name_cache[stock_code] = stock_name
                return stock_name

            return None

        def _get_name_from_tencent(self, stock_code):
            """从腾讯财经API获取股票名称"""
            try:
                if stock_code.startswith('6'):
                    full_code = f"sh{stock_code}"
                else:
                    full_code = f"sz{stock_code}"

                api_url = f"http://qt.gtimg.cn/q={full_code}"
                response = requests.get(api_url, timeout=5)

                if response.status_code == 200:
                    content = response.text
                    # 腾讯API格式: v_sz000001="1~平安银行~000001~..."
                    if '="' in content and '~' in content:
                        start = content.find('="') + 2
                        parts = content[start:].split('~')
                        if len(parts) > 1:
                            stock_name = parts[1].strip()
                            if stock_name:
                                return stock_name
                return None
            except Exception as e:
                print(f"腾讯API获取股票名称失败: {e}")
                return None

        def _get_name_from_eastmoney(self, stock_code):
            """从东方财富API获取股票名称"""
            try:
                if stock_code.startswith('6'):
                    secid = f"1.{stock_code}"  # 上海
                else:
                    secid = f"0.{stock_code}"  # 深圳

                api_url = f"http://push2.eastmoney.com/api/qt/stock/get?secid={secid}&fields=f57,f58"
                response = requests.get(api_url, timeout=5)

                if response.status_code == 200:
                    data = response.json()
                    if 'data' in data and data['data']:
                        stock_name = data['data'].get('f58', '').strip()
                        if stock_name:
                            return stock_name
                return None
            except Exception as e:
                print(f"东方财富API获取股票名称失败: {e}")
                return None

        def _is_valid_stock_code(self, code):
            """验证股票代码格式是否有效"""
            if not code or not code.isdigit():
                return False

            # A股：6位数字
            if len(code) == 6:
                # 上海：6开头
                if code.startswith('6'):
                    return True
                # 深圳：0、3开头
                if code.startswith(('0', '3')):
                    return True
                # 北交所：8、4开头
                if code.startswith(('8', '4')):
                    return True

            return False

        def get_stock_code_by_name(self, stock_name):
            """通过多个API查找股票代码"""
            # 方案1: 尝试东方财富API
            stock_code = self._get_code_from_eastmoney(stock_name)
            if stock_code:
                return stock_code

            # 方案2: 尝试腾讯财经API
            stock_code = self._get_code_from_tencent(stock_name)
            if stock_code:
                return stock_code

            # 方案3: 尝试北交所专门API
            stock_code = self._get_code_from_beijiao(stock_name)
            if stock_code:
                return stock_code

            return None

        def _get_code_from_eastmoney(self, stock_name):
            """从东方财富API获取股票代码"""
            try:
                # 使用东方财富搜索API
                search_url = f"http://searchapi.eastmoney.com/api/suggest/get?input={stock_name}&type=14&token=D43BF722C8E33BDC906FB84D85E326E8"

                response = requests.get(search_url, timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    if 'QuotationCodeTable' in data and 'Data' in data['QuotationCodeTable']:
                        items = data['QuotationCodeTable']['Data']
                        if items and len(items) > 0:
                            for item in items:
                                # 检查是否为A股或北交所
                                if 'SecurityType' in item and item['SecurityType'] in ['1', '2', '8', '27']:  # 1-上海, 2-深圳, 8-北交所, 27-北交所新
                                    if 'Code' in item and 'Name' in item:
                                        code = item['Code']
                                        name = item['Name']
                                        # 检查名称是否匹配
                                        if stock_name in name or name in stock_name:
                                            print(f"东方财富API匹配: {name} -> {code} (SecurityType: {item['SecurityType']})")
                                            return code
                return None
            except Exception as e:
                print(f"东方财富API获取股票代码失败: {e}")
                return None

        def _get_code_from_tencent(self, stock_name):
            """从腾讯财经API获取股票代码"""
            try:
                # 使用腾讯财经搜索API
                search_url = f"http://smartbox.gtimg.cn/s3/?v=2&q={stock_name}&t=all"

                response = requests.get(search_url, timeout=5)
                if response.status_code == 200:
                    content = response.text
                    # 腾讯API格式: v_hint="sz~000001~平安银行~payh~GP-A"
                    if 'v_hint=' in content and '~' in content:
                        # 提取数据部分
                        start = content.find('v_hint="') + 8
                        end = content.find('";', start)
                        data_part = content[start:end]

                        # 按分号分割多个结果
                        results = data_part.split(';')
                        for result in results:
                            # 按波浪线分割字段
                            fields = result.split('~')
                            if len(fields) >= 3:
                                market = fields[0]  # sz、sh或bj
                                code = fields[1]    # 股票代码
                                name = fields[2]    # 股票名称

                                # 检查是否为A股或北交所市场
                                if market in ['sz', 'sh', 'bj'] and code.isdigit() and len(code) >= 6:
                                    # 解码Unicode字符
                                    try:
                                        import codecs
                                        decoded_name = codecs.decode(name, 'unicode_escape')
                                    except:
                                        decoded_name = name

                                    # 检查名称是否匹配
                                    if stock_name in decoded_name or decoded_name in stock_name:
                                        print(f"腾讯API匹配: {decoded_name} -> {code} (市场: {market})")
                                        return code
                return None
            except Exception as e:
                print(f"腾讯API获取股票代码失败: {e}")
                return None

        def _get_code_from_beijiao(self, stock_name):
            """从北交所专门API获取股票代码"""
            try:
                # 使用东方财富的北交所专门搜索
                search_url = f"http://searchapi.eastmoney.com/api/suggest/get?input={stock_name}&type=14&token=D43BF722C8E33BDC906FB84D85E326E8"

                response = requests.get(search_url, timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    if 'QuotationCodeTable' in data and 'Data' in data['QuotationCodeTable']:
                        items = data['QuotationCodeTable']['Data']
                        if items and len(items) > 0:
                            for item in items:
                                # 专门查找北交所股票
                                if 'SecurityType' in item and item['SecurityType'] in ['8', '27']:  # 8-北交所, 27-北交所新
                                    if 'Code' in item and 'Name' in item:
                                        code = item['Code']
                                        name = item['Name']
                                        # 检查名称是否匹配
                                        if stock_name in name or name in stock_name:
                                            print(f"北交所API匹配: {name} -> {code} (SecurityType: {item['SecurityType']})")
                                            return code

                                # 也检查其他可能的北交所标识
                                if 'Code' in item and 'Name' in item:
                                    code = item['Code']
                                    name = item['Name']
                                    # 北交所股票代码通常以8开头且为6位或以4、8开头且为6位
                                    if (code.startswith('8') or code.startswith('4')) and len(code) == 6:
                                        if stock_name in name or name in stock_name:
                                            print(f"北交所代码匹配: {name} -> {code}")
                                            return code

                # 尝试新三板转北交所的API
                search_url2 = f"http://push2.eastmoney.com/api/qt/clist/get?pn=1&pz=50&po=1&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&fid=f3&fs=m:0+t:81+s:2048&fields=f1,f2,f3,f12,f13,f14"

                response2 = requests.get(search_url2, timeout=5)
                if response2.status_code == 200:
                    data2 = response2.json()
                    if 'data' in data2 and data2['data'] and 'diff' in data2['data']:
                        for item in data2['data']['diff']:
                            if 'f14' in item and 'f12' in item:
                                name = item['f14']
                                code = item['f12']
                                if stock_name in name or name in stock_name:
                                    print(f"北交所列表匹配: {name} -> {code}")
                                    return code

                return None
            except Exception as e:
                print(f"北交所API获取股票代码失败: {e}")
                return None

        def get_dazhihui_stock_code(self):
            """从大智慧窗口标题获取股票代码"""
            try:
                # 查找所有包含"大智慧"的窗口
                all_windows = gw.getAllWindows()
                dazhihui_windows = [w for w in all_windows if "大智慧" in w.title and w.visible]

                if not dazhihui_windows:
                    print("未找到大智慧窗口")
                    return None

                print(f"找到 {len(dazhihui_windows)} 个大智慧窗口")

                # 遍历所有大智慧窗口，寻找包含股票信息的窗口
                for window in dazhihui_windows:
                    title = window.title
                    print(f"检测窗口: {title}")  # 调试信息

                    # 跳过插件自身窗口
                    if "涨停解读" in title or "Visual Studio Code" in title:
                        continue

                    # 匹配大智慧窗口标题格式: 大智慧 - [股票名称]
                    stock_name_pattern = r'大智慧\s*-\s*\[([^\]]+)\]'
                    match = re.search(stock_name_pattern, title)

                    if match:
                        stock_name = match.group(1)
                        print(f"提取到股票名称: {stock_name}")  # 调试信息

                        # 通过股票名称查找股票代码
                        stock_code = self.get_stock_code_by_name(stock_name)
                        if stock_code:
                            print(f"找到对应股票代码: {stock_code}")
                            return stock_code
                        else:
                            print(f"未找到股票名称 '{stock_name}' 对应的代码")

                    # 备用方案：直接匹配股票代码（支持A股和北交所）
                    code_patterns = [
                        r'(\d{6})',                    # 基本6位数字（A股和北交所）
                        r'[^\d](\d{6})[^\d]',         # 前后有非数字字符的6位数字
                        r'(\d{6})\]',                 # 6位数字后跟]
                        r'\[(\d{6})',                 # [后跟6位数字
                        r'(8\d{5})',                  # 北交所：8开头的6位数字
                        r'(4\d{5})',                  # 北交所：4开头的6位数字
                    ]

                    for pattern in code_patterns:
                        match = re.search(pattern, title)
                        if match:
                            stock_code = match.group(1)
                            # 验证股票代码格式
                            if self._is_valid_stock_code(stock_code):
                                print(f"直接提取到股票代码: {stock_code}")  # 调试信息
                                return stock_code

                print("所有大智慧窗口都未包含股票信息")
                return None

            except Exception as e:
                print(f"获取大智慧股票代码失败: {e}")
                return None

        def get_tongdaxin_stock_code(self):
            """从通达信窗口标题获取股票代码"""
            try:
                # 查找所有包含"通达信"的窗口
                all_windows = gw.getAllWindows()
                tongdaxin_windows = [w for w in all_windows if "通达信" in w.title and w.visible]

                if not tongdaxin_windows:
                    print("未找到通达信窗口")
                    return None

                print(f"找到 {len(tongdaxin_windows)} 个通达信窗口")

                # 遍历所有通达信窗口，寻找包含股票信息的窗口
                for window in tongdaxin_windows:
                    title = window.title
                    print(f"检测窗口: {title}")  # 调试信息

                    # 跳过插件自身窗口
                    if "涨停解读" in title or "异动解读" in title or "Visual Studio Code" in title:
                        continue

                    # 匹配通达信窗口标题格式: [分析图表-股票名称]
                    stock_name_pattern = r'\[分析图表-([^\]]+)\]'
                    match = re.search(stock_name_pattern, title)

                    if match:
                        stock_name = match.group(1)
                        print(f"提取到股票名称: {stock_name}")  # 调试信息

                        # 通过股票名称查找股票代码
                        stock_code = self.get_stock_code_by_name(stock_name)
                        if stock_code:
                            print(f"找到对应股票代码: {stock_code}")
                            return stock_code
                        else:
                            print(f"未找到股票名称 '{stock_name}' 对应的代码")

                    # 备用方案：直接匹配股票代码（支持A股和北交所）
                    code_patterns = [
                        r'(\d{6})',                    # 基本6位数字（A股和北交所）
                        r'[^\d](\d{6})[^\d]',         # 前后有非数字字符的6位数字
                        r'(\d{6})\]',                 # 6位数字后跟]
                        r'\[(\d{6})',                 # [后跟6位数字
                        r'(8\d{5})',                  # 北交所：8开头的6位数字
                        r'(4\d{5})',                  # 北交所：4开头的6位数字
                    ]

                    for pattern in code_patterns:
                        match = re.search(pattern, title)
                        if match:
                            stock_code = match.group(1)
                            # 验证股票代码格式
                            if self._is_valid_stock_code(stock_code):
                                print(f"直接提取到股票代码: {stock_code}")  # 调试信息
                                return stock_code

                print("所有通达信窗口都未包含股票信息")
                return None

            except Exception as e:
                print(f"获取通达信股票代码失败: {e}")
                return None

        def auto_get_stock(self):
            """自动获取股票代码（根据用户选择的软件）"""
            dazhihui_selected = self.dazhihui_enabled.get()
            tongdaxin_selected = self.tongdaxin_enabled.get()

            # 检查是否选择了软件
            if not dazhihui_selected and not tongdaxin_selected:
                self.status_label.config(text="请先选择要监控的软件", foreground="red")
                messagebox.showwarning("提示", "请先勾选要监控的软件（大智慧或通达信）")
                return

            self.status_label.config(text="正在检测窗口...", foreground="orange")
            stock_code = None

            # 根据选择的软件获取股票代码
            if dazhihui_selected:
                stock_code = self.get_dazhihui_stock_code()
                if stock_code:
                    self.status_label.config(text="从大智慧获取成功", foreground="green")

            if not stock_code and tongdaxin_selected:
                stock_code = self.get_tongdaxin_stock_code()
                if stock_code:
                    self.status_label.config(text="从通达信获取成功", foreground="green")

            if stock_code:
                self.stock_entry.delete(0, tk.END)
                self.stock_entry.insert(0, stock_code)
                self.query_stock_data(stock_code)
            else:
                self.status_label.config(text="未检测到股票", foreground="red")

                # 根据选择的软件显示不同的提示信息
                selected_software = []
                if dazhihui_selected:
                    selected_software.append("大智慧")
                if tongdaxin_selected:
                    selected_software.append("通达信")

                software_text = "、".join(selected_software)
                messagebox.showwarning("提示", f"未能从{software_text}窗口获取股票代码\n\n可能的原因:\n1. {software_text}软件未运行\n2. 未打开具体股票页面\n3. 窗口标题格式不匹配\n\n请尝试手动输入股票代码")

        def on_software_selection_change(self):
            """软件选择变化回调"""
            dazhihui_selected = self.dazhihui_enabled.get()
            tongdaxin_selected = self.tongdaxin_enabled.get()

            if not dazhihui_selected and not tongdaxin_selected:
                self.status_label.config(text="请选择要监控的软件", foreground="orange")
            else:
                selected_software = []
                if dazhihui_selected:
                    selected_software.append("大智慧")
                if tongdaxin_selected:
                    selected_software.append("通达信")
                self.status_label.config(text=f"已选择: {', '.join(selected_software)}", foreground="green")

        def on_manual_query(self, event=None):
            """手动查询股票"""
            stock_code = self.stock_entry.get().strip()
            if stock_code:
                self.query_stock_data(stock_code)
            else:
                messagebox.showwarning("提示", "请输入股票代码")

        def test_window_detection(self):
            """测试窗口检测功能"""
            try:
                all_windows = gw.getAllWindows()
                result = "=== 窗口检测测试 ===\n\n"
                result += f"总窗口数: {len(all_windows)}\n\n"

                # 显示所有包含"大智慧"的窗口
                dazhihui_windows = [w for w in all_windows if "大智慧" in w.title]
                result += f"大智慧相关窗口 ({len(dazhihui_windows)} 个):\n"

                for i, window in enumerate(dazhihui_windows):
                    result += f"{i+1}. 标题: {window.title}\n"
                    result += f"   位置: ({window.left}, {window.top})\n"
                    result += f"   大小: {window.width}x{window.height}\n"
                    result += f"   可见: {window.visible}\n"
                    result += f"   激活: {window.isActive}\n\n"

                # 显示所有包含"通达信"的窗口
                tongdaxin_windows = [w for w in all_windows if "通达信" in w.title]
                result += f"通达信相关窗口 ({len(tongdaxin_windows)} 个):\n"

                for i, window in enumerate(tongdaxin_windows):
                    result += f"{i+1}. 标题: {window.title}\n"
                    result += f"   位置: ({window.left}, {window.top})\n"
                    result += f"   大小: {window.width}x{window.height}\n"
                    result += f"   可见: {window.visible}\n"
                    result += f"   激活: {window.isActive}\n\n"

                # 显示前10个窗口的标题
                result += "\n前10个窗口标题:\n"
                for i, window in enumerate(all_windows[:10]):
                    result += f"{i+1}. {window.title}\n"

                self.text_area.delete(1.0, tk.END)
                self.text_area.insert(tk.END, result, "detail")

            except Exception as e:
                self.text_area.delete(1.0, tk.END)
                self.text_area.insert(tk.END, f"窗口检测测试失败: {e}\n", "detail")

        def query_stock_data(self, stock_code):
            """查询股票涨停数据"""
            self.status_label.config(text="查询中...", foreground="orange")
            self.current_stock = stock_code
            self.current_stock_name = None  # 重置股票名称

            # 在后台线程中查询数据（包括获取股票名称）
            threading.Thread(target=self._query_data_thread, args=(stock_code,), daemon=True).start()

        def _query_data_thread(self, stock_code):
            """后台查询数据线程"""
            try:
                # 获取完整涨停数据（历史+当日）
                result = self.api.get_complete_limit_data(stock_code, include_today=True)
                stock_name = self.get_stock_name_by_code(stock_code)

                # 在主线程中更新UI
                self.window.after(0, self._update_display, result, stock_name)

            except Exception as e:
                self.window.after(0, self._show_error, f"查询失败: {str(e)}")

        def _update_display(self, result, stock_name=None):
            """更新显示内容"""
            self.text_area.delete(1.0, tk.END)

            if "error" in result:
                self.status_label.config(text="查询失败", foreground="red")
                self.text_area.insert(tk.END, f"❌ 查询失败: {result['error']}\n")
                return

            stock_id = result.get('StockID', 'Unknown')
            limit_list = result.get('List', [])
            has_today = result.get('HasTodayLimit', False)

            # 更新当前股票名称
            if stock_name:
                self.current_stock_name = stock_name

            # 更新基本信息 - 显示股票代码+名称
            if self.current_stock_name:
                stock_display = f"股票: {stock_id}，{self.current_stock_name}"
            else:
                stock_display = f"股票: {stock_id}"
            self.stock_info_label.config(text=stock_display)

            # 显示涨停次数，如果有当日涨停则特别标注
            count_text = f"涨停次数: {len(limit_list)}"
            if has_today:
                count_text += " (含当日)"
            self.limit_count_label.config(text=count_text)

            if not limit_list:
                self.text_area.insert(tk.END, "📊 该股票暂无涨停记录\n", "header")
                self.status_label.config(text="查询完成", foreground="green")
                return

            # 显示涨停记录 - 显示股票代码+名称
            if self.current_stock_name:
                header_text = f"📈 {stock_id}，{self.current_stock_name} 涨停解读 (共{len(limit_list)}次"
            else:
                header_text = f"📈 {stock_id} 涨停解读 (共{len(limit_list)}次"

            if has_today:
                header_text += "，含当日涨停"
            header_text += ")\n"

            self.text_area.insert(tk.END, header_text, "header")
            self.text_area.insert(tk.END, "=" * 80 + "\n", "separator")

            for item in limit_list:
                date = item.get('Date', 'N/A')
                reason = item.get('Reason', 'N/A')
                sclt = item.get('SCLT', '')
                is_today = item.get('IsToday', False)

                # 日期 - 如果是当日涨停，特殊标记
                if is_today:
                    self.text_area.insert(tk.END, f"\n🔥 {date} [当日涨停]", "date")
                else:
                    self.text_area.insert(tk.END, f"\n📅 {date}", "date")

                if sclt:
                    self.text_area.insert(tk.END, f" ({sclt})", "date")
                self.text_area.insert(tk.END, "\n")

                # 涨停原因 - 去掉"详情"描述，只显示原因
                self.text_area.insert(tk.END, f"💡 原因: {reason}\n", "reason")

                self.text_area.insert(tk.END, "-" * 60 + "\n", "separator")

            # 统计信息
            self.text_area.insert(tk.END, f"\n📊 统计信息:\n", "header")
            self.text_area.insert(tk.END, f"   • 总涨停次数: {len(limit_list)}\n", "detail")

            if limit_list:
                latest_date = limit_list[0].get('Date', 'N/A')
                self.text_area.insert(tk.END, f"   • 最近涨停: {latest_date}\n", "detail")

            # 滚动到顶部
            self.text_area.see(1.0)

            self.status_label.config(text="查询完成", foreground="green")

        def _show_error(self, error_msg):
            """显示错误信息"""
            self.status_label.config(text="查询失败", foreground="red")
            messagebox.showerror("错误", error_msg)

        def toggle_auto_refresh(self):
            """
            切换自动刷新功能

            自动刷新规则：
            - 启用后，每5秒自动刷新当前选中股票的涨停数据
            - 只有在已选择股票的情况下才会刷新
            - 刷新时会重新获取历史+当日涨停数据
            - 可以实时获取最新的当日涨停信息
            """
            self.auto_refresh = self.auto_refresh_var.get()
            if self.auto_refresh:
                self.start_auto_refresh()
                safe_print("自动刷新已启用 - 每5秒刷新一次当前股票数据")
            else:
                self.stop_auto_refresh()
                safe_print("自动刷新已停用")

        def toggle_auto_monitor(self):
            """
            切换自动监控功能

            自动监控规则：
            - 启用后，每2秒检测大智慧窗口标题变化
            - 当检测到窗口标题包含新的股票信息时，自动查询该股票
            - 支持格式：大智慧 - [股票名称]
            - 自动通过股票名称查找对应的股票代码
            - 查询成功后自动显示涨停解读信息
            """
            if self.auto_monitor_var.get():
                self.start_auto_monitor()
                safe_print("自动监控已启用 - 监控大智慧窗口变化")
            else:
                self.stop_auto_monitor()
                safe_print("自动监控已停用")

        def start_auto_monitor(self):
            """
            启动自动监控

            监控机制：
            1. 创建后台线程，每2秒检查一次大智慧窗口
            2. 检测窗口标题格式：大智慧 - [股票名称]
            3. 当标题变化时，自动提取股票名称并查询对应代码
            4. 自动更新界面显示新股票的涨停信息
            """
            if not self.auto_monitor_running:
                self.auto_monitor_running = True
                self.auto_monitor_thread = threading.Thread(target=self.auto_monitor_loop, daemon=True)
                self.auto_monitor_thread.start()
                self.status_label.config(text="自动监控已启动", foreground="green")

        def stop_auto_monitor(self):
            """停止自动监控"""
            self.auto_monitor_running = False
            if self.auto_monitor_thread:
                self.auto_monitor_thread = None
            self.status_label.config(text="自动监控已停止", foreground="orange")

        def auto_monitor_loop(self):
            """
            自动监控循环

            工作流程：
            1. 每2秒扫描一次所有窗口
            2. 查找包含"大智慧"的窗口
            3. 检查窗口标题是否包含股票信息
            4. 与上次记录的标题对比，如有变化则触发查询
            5. 异常时等待5秒后重试
            """
            while self.auto_monitor_running:
                try:
                    # 获取当前软件窗口标题（根据用户选择）
                    current_title = self.get_current_software_title()

                    # 如果窗口标题发生变化，自动获取股票信息
                    if current_title and current_title != self.last_window_title:
                        self.last_window_title = current_title
                        safe_print(f"检测到窗口变化: {current_title}")

                        # 在主线程中执行UI更新
                        self.window.after(0, self.auto_get_stock_from_title, current_title)

                    time.sleep(2)  # 每2秒检查一次

                except Exception as e:
                    safe_print(f"自动监控异常: {e}")
                    time.sleep(5)  # 异常时等待更长时间

        def start_auto_refresh(self):
            """
            开始自动刷新

            刷新机制：
            1. 检查是否启用自动刷新且已选择股票
            2. 如果条件满足，重新查询当前股票的完整数据（历史+当日）
            3. 设置定时器，5秒后再次执行刷新
            4. 可以实时获取当日涨停的最新信息
            """
            if self.auto_refresh and self.current_stock:
                safe_print(f"自动刷新: {self.current_stock}")
                self.query_stock_data(self.current_stock)
                # 设置下次刷新
                self.window.after(self.refresh_interval * 1000, self.start_auto_refresh)
            elif self.auto_refresh and not self.current_stock:
                # 如果启用了自动刷新但没有选择股票，继续等待
                self.window.after(self.refresh_interval * 1000, self.start_auto_refresh)

        def stop_auto_refresh(self):
            """停止自动刷新"""
            self.auto_refresh = False
            safe_print("自动刷新已停止")

        def refresh_data(self):
            """
            手动刷新数据

            功能：
            - 立即刷新当前选中股票的涨停数据
            - 获取最新的历史记录和当日涨停信息
            - 如果未选择股票则提示用户
            """
            if self.current_stock:
                safe_print(f"手动刷新: {self.current_stock}")
                self.query_stock_data(self.current_stock)
            else:
                messagebox.showinfo("提示", "请先选择股票")

        def get_current_software_title(self):
            """获取当前软件窗口标题（根据用户选择）"""
            try:
                all_windows = gw.getAllWindows()

                # 根据用户选择检查不同的软件
                if self.dazhihui_enabled.get():
                    dazhihui_windows = [w for w in all_windows if "大智慧" in w.title and w.visible]
                    for window in dazhihui_windows:
                        # 跳过插件自身窗口
                        if "涨停解读" in window.title or "异动解读" in window.title or "Visual Studio Code" in window.title:
                            continue
                        # 查找包含股票信息的窗口
                        if re.search(r'大智慧\s*-\s*\[([^\]]+)\]', window.title):
                            return window.title

                if self.tongdaxin_enabled.get():
                    tongdaxin_windows = [w for w in all_windows if "通达信" in w.title and w.visible]
                    for window in tongdaxin_windows:
                        # 跳过插件自身窗口
                        if "涨停解读" in window.title or "异动解读" in window.title or "Visual Studio Code" in window.title:
                            continue
                        # 查找包含股票信息的窗口
                        if re.search(r'\[分析图表-([^\]]+)\]', window.title):
                            return window.title

                return None

            except Exception as e:
                print(f"获取软件窗口标题失败: {e}")
                return None

        def auto_get_stock_from_title(self, title):
            """从窗口标题自动获取股票信息"""
            try:
                stock_name = None
                software_name = ""

                # 尝试从大智慧窗口提取股票名称
                match = re.search(r'大智慧\s*-\s*\[([^\]]+)\]', title)
                if match:
                    stock_name = match.group(1)
                    software_name = "大智慧"

                # 尝试从通达信窗口提取股票名称
                if not stock_name:
                    match = re.search(r'\[分析图表-([^\]]+)\]', title)
                    if match:
                        stock_name = match.group(1)
                        software_name = "通达信"

                if stock_name:
                    print(f"从{software_name}自动检测到股票: {stock_name}")

                    # 查找股票代码
                    stock_code = self.get_stock_code_by_name(stock_name)
                    if stock_code:
                        # 更新输入框
                        self.stock_entry.delete(0, tk.END)
                        self.stock_entry.insert(0, stock_code)

                        # 存储股票名称
                        self.current_stock_name = stock_name

                        # 查询股票数据
                        self.query_stock_data(stock_code)
                        self.status_label.config(text=f"自动获取({software_name}): {stock_name}({stock_code})", foreground="blue")
                    else:
                        self.status_label.config(text=f"未找到股票代码: {stock_name}", foreground="red")
                else:
                    print(f"无法从窗口标题提取股票信息: {title}")

            except Exception as e:
                print(f"自动获取股票信息失败: {e}")
                self.status_label.config(text="自动获取失败", foreground="red")

        def get_resource_path(self, relative_path):
            """获取资源文件的绝对路径，支持exe打包"""
            try:
                # PyInstaller创建临时文件夹，并将路径存储在_MEIPASS中
                base_path = sys._MEIPASS
            except Exception:
                # 开发环境中使用当前目录
                base_path = os.path.abspath(".")
            return os.path.join(base_path, relative_path)

        def show_startup_dialog(self):
            """显示启动弹窗"""
            import datetime
            import os

            # 检查今日是否已经显示过弹窗
            today = datetime.date.today().strftime("%Y-%m-%d")
            flag_file = f"no_popup_{today}.flag"

            if os.path.exists(flag_file):
                return  # 今日已经选择不再弹出

            # 创建弹窗窗口
            dialog = tk.Toplevel(self.window)
            dialog.title("异动解读工具v5.0")
            dialog.geometry("400x500")
            dialog.resizable(False, False)
            dialog.configure(bg='white')

            # 设置窗口居中
            dialog.transient(self.window)
            dialog.grab_set()

            # 主框架
            main_frame = tk.Frame(dialog, bg='white', padx=20, pady=20)
            main_frame.pack(fill=tk.BOTH, expand=True)

            # 标题
            title_label = tk.Label(main_frame, text="异动解读工具v5.0",
                                 font=("Microsoft YaHei", 16, "bold"), bg='white', fg='#333333')
            title_label.pack(pady=(0, 10))

            # 说明文本 - 使用中文字体避免乱码
            info_text = "本软件仅用于学习交流。\n联系作者QQ：2267617536"
            info_label = tk.Label(main_frame, text=info_text,
                                font=("Microsoft YaHei", 12), bg='white', fg='#666666',
                                justify=tk.CENTER)
            info_label.pack(pady=(0, 20))

            # 图片框架
            image_frame = tk.Frame(main_frame, bg='white')
            image_frame.pack(pady=(0, 20))

            # 图片标题
            image_title_frame = tk.Frame(image_frame, bg='white')
            image_title_frame.pack(fill=tk.X, pady=(0, 10))

            tk.Label(image_title_frame, text="扫码赞赏作者",
                    font=("Microsoft YaHei", 10), bg='white', fg='#333333').pack(side=tk.LEFT, expand=True)
            tk.Label(image_title_frame, text="扫码进交流群",
                    font=("Microsoft YaHei", 10), bg='white', fg='#333333').pack(side=tk.RIGHT, expand=True)

            # 图片显示框架
            images_frame = tk.Frame(image_frame, bg='white')
            images_frame.pack()

            # 尝试加载图片
            try:
                from PIL import Image, ImageTk

                # 加载赞赏码图片 - 使用正确的资源路径
                reward_img_path = self.get_resource_path("赞赏码.png")
                if os.path.exists(reward_img_path):
                    reward_img = Image.open(reward_img_path)
                    reward_img = reward_img.resize((120, 120), Image.Resampling.LANCZOS)
                    reward_photo = ImageTk.PhotoImage(reward_img)
                    reward_label = tk.Label(images_frame, image=reward_photo, bg='white')
                    reward_label.image = reward_photo  # 保持引用
                    reward_label.pack(side=tk.LEFT, padx=(0, 20))
                else:
                    tk.Label(images_frame, text="赞赏码\n(图片未找到)",
                            font=("Microsoft YaHei", 9), bg='white', fg='#999999',
                            width=15, height=8, relief=tk.SUNKEN).pack(side=tk.LEFT, padx=(0, 20))

                # 加载交流群图片 - 使用正确的资源路径
                friend_img_path = self.get_resource_path("加好友.png")
                if os.path.exists(friend_img_path):
                    friend_img = Image.open(friend_img_path)
                    friend_img = friend_img.resize((120, 120), Image.Resampling.LANCZOS)
                    friend_photo = ImageTk.PhotoImage(friend_img)
                    friend_label = tk.Label(images_frame, image=friend_photo, bg='white')
                    friend_label.image = friend_photo  # 保持引用
                    friend_label.pack(side=tk.RIGHT)
                else:
                    tk.Label(images_frame, text="交流群\n(图片未找到)",
                            font=("Microsoft YaHei", 9), bg='white', fg='#999999',
                            width=15, height=8, relief=tk.SUNKEN).pack(side=tk.RIGHT)

            except ImportError:
                # 如果没有PIL库，显示文本提示
                tk.Label(images_frame, text="赞赏码\n(需要PIL库)",
                        font=("Microsoft YaHei", 9), bg='white', fg='#999999',
                        width=15, height=8, relief=tk.SUNKEN).pack(side=tk.LEFT, padx=(0, 20))
                tk.Label(images_frame, text="交流群\n(需要PIL库)",
                        font=("Microsoft YaHei", 9), bg='white', fg='#999999',
                        width=15, height=8, relief=tk.SUNKEN).pack(side=tk.RIGHT)
            except Exception as e:
                print(f"加载图片失败: {e}")
                tk.Label(images_frame, text="图片加载失败",
                        font=("Microsoft YaHei", 9), bg='white', fg='#999999').pack()

            # 底部控制框架
            bottom_frame = tk.Frame(main_frame, bg='white')
            bottom_frame.pack(fill=tk.X, pady=(20, 0))

            # "本日不再弹出"选项
            no_popup_var = tk.BooleanVar()
            no_popup_check = tk.Checkbutton(bottom_frame, text="本日不再弹出",
                                          variable=no_popup_var, bg='white',
                                          font=("Microsoft YaHei", 10))
            no_popup_check.pack(side=tk.LEFT)

            # 关闭按钮
            def close_dialog():
                if no_popup_var.get():
                    # 创建标记文件
                    with open(flag_file, 'w') as f:
                        f.write(today)
                dialog.destroy()

            close_btn = tk.Button(bottom_frame, text="确定", command=close_dialog,
                                bg='#4CAF50', fg='white', font=("Microsoft YaHei", 10),
                                padx=20, pady=5)
            close_btn.pack(side=tk.RIGHT)

        def run(self):
            """运行插件"""
            try:
                self.window.mainloop()
            except KeyboardInterrupt:
                print("插件已退出")

    # 创建并运行应用
    try:
        app = ZhangTingJieDu()
        app.run()
    except Exception as e:
        print(f"应用启动失败: {e}")
        messagebox.showerror("错误", f"应用启动失败: {e}")

def main():
    """主函数"""
    try:
        print("启动异动解读工具 v5.0 (加密验证版)...")
        print("=" * 60)
    except UnicodeEncodeError:
        print("Starting ZhangTing Tool v5.0 (Encrypted)...")
        print("=" * 60)

    # 对于exe版本，跳过依赖检查（假设已经打包了所有依赖）
    try:
        # 检查是否为exe环境
        import sys
        if getattr(sys, 'frozen', False):
            # 运行在exe环境中，跳过依赖检查
            safe_print("exe环境检测，跳过依赖检查...")
        else:
            # 开发环境，执行依赖检查
            if not check_and_install_dependencies():
                safe_print("依赖库安装失败，程序无法启动")
                # 在开发环境中保留input，但在exe中避免
                try:
                    input("按回车键退出...")
                except:
                    pass
                return
    except Exception as e:
        safe_print(f"依赖检查异常: {e}")

    # 执行卡密验证
    if not AUTH_CONFIG['DEMO_MODE']:
        safe_print("\n正在进行卡密验证...")
        if not perform_license_validation():
            safe_print("验证失败，程序退出")
            try:
                import sys
                if not getattr(sys, 'frozen', False):
                    input("按回车键退出...")
            except:
                pass
            return
    else:
        safe_print("\n演示模式：跳过验证")

    safe_print("\n启动主程序...")

    try:
        # 启动主程序
        main_program()
    except Exception as e:
        safe_print(f"程序启动失败: {e}")
        # 在exe环境中避免使用input
        try:
            import sys
            if not getattr(sys, 'frozen', False):
                input("按回车键退出...")
        except:
            pass

if __name__ == "__main__":
    main()
