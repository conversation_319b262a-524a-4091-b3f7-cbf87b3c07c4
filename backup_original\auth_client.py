#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python客户端验证模块
提供HWID获取、网络验证、加密通信功能

功能特性:
- 硬件ID获取 (CPU + 主板 + MAC地址)
- AES加密通信
- 请求签名验证
- 防调试检测
- 网络验证API调用

作者: 股中掘金
版本: 1.0
"""

import os
import sys
import time
import json
import hmac
import hashlib
import base64
import platform
import subprocess
import uuid
import requests
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

class AntiDebug:
    """反调试检测"""
    
    @staticmethod
    def check_debugger():
        """检测调试器"""
        try:
            # 检测常见调试器进程
            debug_processes = [
                'ollydbg.exe', 'x64dbg.exe', 'x32dbg.exe', 
                'windbg.exe', 'ida.exe', 'ida64.exe',
                'cheatengine.exe', 'processhacker.exe'
            ]
            
            if platform.system() == 'Windows':
                import psutil
                running_processes = [p.name().lower() for p in psutil.process_iter()]
                for debug_proc in debug_processes:
                    if debug_proc.lower() in running_processes:
                        return True
            
            # 检测调试标志
            if hasattr(sys, 'gettrace') and sys.gettrace() is not None:
                return True
                
            return False
        except:
            return False
    
    @staticmethod
    def check_vm():
        """检测虚拟机环境"""
        try:
            # 检测虚拟机特征
            vm_indicators = [
                'vmware', 'virtualbox', 'vbox', 'qemu', 
                'xen', 'hyper-v', 'parallels'
            ]
            
            # 检查系统信息
            system_info = platform.platform().lower()
            for indicator in vm_indicators:
                if indicator in system_info:
                    return True
            
            # Windows特定检测
            if platform.system() == 'Windows':
                try:
                    import wmi
                    c = wmi.WMI()
                    for system in c.Win32_ComputerSystem():
                        if any(vm in system.Model.lower() for vm in vm_indicators):
                            return True
                except:
                    pass
            
            return False
        except:
            return False

class HWIDGenerator:
    """硬件ID生成器"""
    
    @staticmethod
    def get_cpu_id():
        """获取CPU ID"""
        try:
            if platform.system() == 'Windows':
                result = subprocess.run(
                    ['wmic', 'cpu', 'get', 'ProcessorId', '/value'],
                    capture_output=True, text=True, timeout=10
                )
                for line in result.stdout.split('\n'):
                    if 'ProcessorId=' in line:
                        return line.split('=')[1].strip()
            else:
                # Linux/Mac
                with open('/proc/cpuinfo', 'r') as f:
                    for line in f:
                        if 'serial' in line.lower():
                            return line.split(':')[1].strip()
        except:
            pass
        return platform.processor()
    
    @staticmethod
    def get_motherboard_id():
        """获取主板ID"""
        try:
            if platform.system() == 'Windows':
                result = subprocess.run(
                    ['wmic', 'baseboard', 'get', 'SerialNumber', '/value'],
                    capture_output=True, text=True, timeout=10
                )
                for line in result.stdout.split('\n'):
                    if 'SerialNumber=' in line:
                        return line.split('=')[1].strip()
            else:
                # Linux
                try:
                    with open('/sys/class/dmi/id/board_serial', 'r') as f:
                        return f.read().strip()
                except:
                    pass
        except:
            pass
        return platform.node()
    
    @staticmethod
    def get_mac_address():
        """获取MAC地址"""
        try:
            mac = uuid.getnode()
            return ':'.join(('%012X' % mac)[i:i+2] for i in range(0, 12, 2))
        except:
            return "00:00:00:00:00:00"
    
    @staticmethod
    def generate_hwid():
        """生成硬件ID"""
        cpu_id = HWIDGenerator.get_cpu_id()
        motherboard_id = HWIDGenerator.get_motherboard_id()
        mac_address = HWIDGenerator.get_mac_address()
        
        # 组合硬件信息
        hw_info = f"{cpu_id}|{motherboard_id}|{mac_address}"
        
        # 生成SHA256哈希
        hwid = hashlib.sha256(hw_info.encode()).hexdigest()
        return hwid

class AuthClient:
    """验证客户端"""

    def __init__(self, server_url, secret_key, project_code="DEFAULT"):
        self.server_url = server_url.rstrip('/')
        self.secret_key = secret_key
        self.project_code = project_code
        self.encryption_key = self._init_encryption()
        self.hwid = HWIDGenerator.generate_hwid()
        self.license_file = f"license_{project_code}.dat"
    
    def _init_encryption(self):
        """初始化加密"""
        password = self.secret_key.encode()
        salt = b'salt_1234567890'  # 与服务器保持一致
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password))
        return Fernet(key)
    
    def encrypt_data(self, data):
        """加密数据"""
        if isinstance(data, dict):
            data = json.dumps(data)
        return self.encryption_key.encrypt(data.encode()).decode()
    
    def decrypt_data(self, encrypted_data):
        """解密数据"""
        try:
            decrypted = self.encryption_key.decrypt(encrypted_data.encode())
            return json.loads(decrypted.decode())
        except Exception as e:
            print(f"解密失败: {e}")
            return None
    
    def create_signature(self, data, timestamp):
        """创建请求签名"""
        message = f"{data}{timestamp}".encode()
        signature = hmac.new(
            self.secret_key.encode(),
            message,
            hashlib.sha256
        ).hexdigest()
        return signature
    
    def save_license(self, key_code):
        """保存卡密到本地"""
        try:
            # 加密保存
            license_data = {
                'key_code': key_code,
                'project_code': self.project_code,
                'hwid': self.hwid,
                'save_time': time.time()
            }

            encrypted_data = self.encrypt_data(license_data)

            with open(self.license_file, 'w', encoding='utf-8') as f:
                f.write(encrypted_data)

            return True
        except Exception:
            return False

    def load_saved_license(self):
        """加载保存的卡密"""
        try:
            if not os.path.exists(self.license_file):
                return None

            with open(self.license_file, 'r', encoding='utf-8') as f:
                encrypted_data = f.read()

            license_data = self.decrypt_data(encrypted_data)
            if not license_data:
                return None

            # 验证数据完整性
            if (license_data.get('project_code') == self.project_code and
                license_data.get('hwid') == self.hwid):
                return license_data.get('key_code')

            return None
        except Exception:
            return None

    def clear_saved_license(self):
        """清除保存的卡密"""
        try:
            if os.path.exists(self.license_file):
                os.remove(self.license_file)
        except Exception:
            pass

    def validate_license(self, key_code=None, auto_save=False):
        """验证卡密"""
        try:
            # 反调试检测
            if AntiDebug.check_debugger():
                print("检测到调试器，程序退出")
                sys.exit(1)

            if AntiDebug.check_vm():
                print("检测到虚拟机环境，程序退出")
                sys.exit(1)

            # 如果没有提供卡密，尝试加载保存的卡密
            if not key_code:
                key_code = self.load_saved_license()
                if not key_code:
                    return False, "未找到保存的卡密"

            # 准备验证数据
            auth_data = {
                'key_code': key_code,
                'project_code': self.project_code,
                'hwid': self.hwid
            }
            
            # 加密数据
            encrypted_data = self.encrypt_data(auth_data)
            
            # 创建时间戳和签名
            timestamp = int(time.time())
            signature = self.create_signature(encrypted_data, timestamp)
            
            # 构建请求
            request_data = {
                'encrypted_data': encrypted_data,
                'signature': signature,
                'timestamp': timestamp
            }
            
            # 发送验证请求
            response = requests.post(
                f"{self.server_url}/api/auth",
                json=request_data,
                timeout=10,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code != 200:
                return False, f"服务器错误: {response.status_code}"
            
            # 解析响应
            response_data = response.json()
            
            if 'encrypted_data' not in response_data:
                return False, "无效的服务器响应"
            
            # 解密响应
            decrypted_response = self.decrypt_data(response_data['encrypted_data'])
            if not decrypted_response:
                return False, "响应解密失败"
            
            success = decrypted_response.get('success', False)
            message = decrypted_response.get('message', '未知错误')

            # 如果验证成功且需要自动保存
            if success and auto_save:
                self.save_license(key_code)
            elif not success:
                # 验证失败时清除保存的卡密
                self.clear_saved_license()

            return success, message
            
        except requests.exceptions.RequestException as e:
            return False, f"网络连接失败: {e}"
        except Exception as e:
            return False, f"验证异常: {e}"
    
    def get_hwid(self):
        """获取硬件ID"""
        return self.hwid

# 便捷函数
def create_auth_client(server_url="http://localhost:5000", secret_key="your-secret-key-change-this", project_code="DEFAULT"):
    """创建验证客户端实例"""
    return AuthClient(server_url, secret_key, project_code)

def quick_validate(key_code, server_url="http://localhost:5000", secret_key="your-secret-key-change-this", project_code="DEFAULT"):
    """快速验证卡密"""
    client = create_auth_client(server_url, secret_key, project_code)
    return client.validate_license(key_code)

# 示例用法
if __name__ == "__main__":
    # 测试验证
    client = create_auth_client()
    print(f"硬件ID: {client.get_hwid()}")
    
    # 测试卡密验证
    test_key = "TEST-KEY-123456"
    success, message = client.validate_license(test_key)
    print(f"验证结果: {success}, 消息: {message}")
